{"level":"dev.info","ts":"[2025-08-27 09:14:37.653]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT id,businessID,username,name,nickname,city,company,avatar,status FROM `business_account` WHERE `id` = ? LIMIT 1, [1]","duration":"28.6636ms","duration_ms":28}
{"level":"dev.info","ts":"[2025-08-27 09:14:37.688]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `common_config` WHERE `keyname` = ? LIMIT 1, [rooturl]","duration":"32.5936ms","duration_ms":32}
{"level":"dev.info","ts":"[2025-08-27 09:33:04.332]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT id,accountID,businessID,password,salt,name,username FROM `business_account` WHERE (`username` = ? or `email` = ?) and `mobile` = ? LIMIT 1, [fincore fincore ***********]","duration":"33.827ms","duration_ms":33}
{"level":"dev.info","ts":"[2025-08-27 09:33:04.396]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `business_account` SET `lastLoginTime` = ?,`lastLoginIp` = ?,`loginstatus` = ? WHERE `id` = ?, [**********  1 1]","duration":"30.9242ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-08-27 09:33:04.426]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_role_access` WHERE `uid` = ?, [1]","duration":"29.4436ms","duration_ms":29}
{"level":"dev.info","ts":"[2025-08-27 09:33:04.456]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_role` WHERE `id` IN (?), [1]","duration":"30.4288ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-08-27 09:33:04.476]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"INSERT INTO `admin_operate_log` (`uid`,`operate_name`,`created_at`,`result`,`detail`,`ip`,`region`) VALUES (?,?,?,?,?,?,?), [1 登录 2025-08-27 09:33:04.4568055 +0800 CST m=+38.********* 成功 成功  未知]","duration":"19.3276ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-27 09:33:04.520]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_role_access` WHERE `uid` = ?, [1]","duration":"27.4361ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-08-27 09:33:04.558]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_role` WHERE `id` IN (?), [1]","duration":"38.4272ms","duration_ms":38}
{"level":"dev.info","ts":"[2025-08-27 09:33:04.602]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT id,businessID,username,name,nickname,city,company,avatar,status FROM `business_account` WHERE `id` = ? LIMIT 1, [1]","duration":"109.3573ms","duration_ms":109}
{"level":"dev.info","ts":"[2025-08-27 09:33:04.612]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `status` = ? and `type` IN (?,?) ORDER BY orderNo asc, [0 0 1]","duration":"53.5252ms","duration_ms":53}
{"level":"dev.info","ts":"[2025-08-27 09:33:04.637]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `common_config` WHERE `keyname` = ? LIMIT 1, [rooturl]","duration":"35.3597ms","duration_ms":35}
{"level":"dev.info","ts":"[2025-08-27 09:33:04.648]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 8]","duration":"35.228ms","duration_ms":35}
{"level":"dev.info","ts":"[2025-08-27 09:33:04.685]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 70]","duration":"37.0739ms","duration_ms":37}
{"level":"dev.info","ts":"[2025-08-27 09:33:04.712]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 69]","duration":"26.7193ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-08-27 09:33:04.751]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 68]","duration":"38.4409ms","duration_ms":38}
{"level":"dev.info","ts":"[2025-08-27 09:33:04.782]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 463]","duration":"30.578ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-08-27 09:33:04.813]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 460]","duration":"31.4788ms","duration_ms":31}
{"level":"dev.info","ts":"[2025-08-27 09:33:04.834]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 451]","duration":"21.0972ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-27 09:33:04.863]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 458]","duration":"28.3261ms","duration_ms":28}
{"level":"dev.info","ts":"[2025-08-27 09:33:04.903]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 450]","duration":"39.4264ms","duration_ms":39}
{"level":"dev.info","ts":"[2025-08-27 09:33:04.921]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 457]","duration":"18.6341ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-27 09:33:04.944]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 456]","duration":"22.6278ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-27 09:33:05.030]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 464]","duration":"86.1904ms","duration_ms":86}
{"level":"dev.info","ts":"[2025-08-27 09:33:05.056]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 465]","duration":"26.0689ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-08-27 09:33:05.096]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 455]","duration":"40.2427ms","duration_ms":40}
{"level":"dev.info","ts":"[2025-08-27 09:33:05.139]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 440]","duration":"43.0495ms","duration_ms":43}
{"level":"dev.info","ts":"[2025-08-27 09:33:05.161]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 439]","duration":"21.6962ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-27 09:33:05.215]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 467]","duration":"53.421ms","duration_ms":53}
{"level":"dev.info","ts":"[2025-08-27 09:33:05.236]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 468]","duration":"21.4179ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-27 09:33:05.293]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 469]","duration":"56.9997ms","duration_ms":56}
{"level":"dev.info","ts":"[2025-08-27 09:33:05.326]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 466]","duration":"33.4282ms","duration_ms":33}
{"level":"dev.info","ts":"[2025-08-27 09:33:05.408]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 453]","duration":"82.1012ms","duration_ms":82}
{"level":"dev.info","ts":"[2025-08-27 09:33:05.469]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 459]","duration":"60.6562ms","duration_ms":60}
{"level":"dev.info","ts":"[2025-08-27 09:33:05.545]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 452]","duration":"75.2169ms","duration_ms":75}
{"level":"dev.info","ts":"[2025-08-27 09:33:05.587]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 438]","duration":"42.4705ms","duration_ms":42}
{"level":"dev.info","ts":"[2025-08-27 09:33:05.606]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 437]","duration":"18.9882ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-27 09:33:05.641]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 435]","duration":"34.1984ms","duration_ms":34}
{"level":"dev.info","ts":"[2025-08-27 09:33:05.696]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 436]","duration":"55.4924ms","duration_ms":55}
{"level":"dev.info","ts":"[2025-08-27 09:33:05.715]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 383]","duration":"19.1747ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-27 09:33:05.751]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 75]","duration":"35.5118ms","duration_ms":35}
{"level":"dev.info","ts":"[2025-08-27 09:33:05.868]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 97]","duration":"117.0236ms","duration_ms":117}
{"level":"dev.info","ts":"[2025-08-27 09:33:05.910]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 374]","duration":"42.6887ms","duration_ms":42}
{"level":"dev.info","ts":"[2025-08-27 09:33:05.959]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 74]","duration":"48.3706ms","duration_ms":48}
{"level":"dev.info","ts":"[2025-08-27 09:33:06.012]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 11]","duration":"53.0641ms","duration_ms":53}
{"level":"dev.info","ts":"[2025-08-27 09:33:06.053]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 12]","duration":"40.9011ms","duration_ms":40}
{"level":"dev.info","ts":"[2025-08-27 09:33:06.132]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 63]","duration":"78.2511ms","duration_ms":78}
{"level":"dev.info","ts":"[2025-08-27 09:33:06.194]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 13]","duration":"60.6916ms","duration_ms":60}
{"level":"dev.info","ts":"[2025-08-27 09:33:06.225]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 61]","duration":"30.6498ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-08-27 09:33:06.604]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb2916f7ed052fb9413","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-27 3 9 2]","duration":"30.5566ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-08-27 09:33:06.604]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb2916f7ed052fb9413","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-27 3 9 2]","duration":"30.5566ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-08-27 09:33:06.627]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb2916f7ed052fb9413","sql":"SELECT COALESCE(SUM(total_due_amount - paid_amount - total_waive_amount), 0) as overdue_amount FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-27 3 9 2]","duration":"21.8304ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-27 09:33:06.627]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb2916f7ed052fb9413","sql":"SELECT COALESCE(SUM(total_due_amount - paid_amount - total_waive_amount), 0) as overdue_amount FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-27 3 9 2]","duration":"22.3481ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-27 09:33:06.648]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb2916f7ed052fb9413","sql":"SELECT * FROM `business_payment_transactions` WHERE `status` = ? and `type` = ? and DATE(completed_at) BETWEEN ? and ?, [2 DISBURSEMENT 2025-08-27 2025-08-27]","duration":"73.7336ms","duration_ms":73}
{"level":"dev.info","ts":"[2025-08-27 09:33:06.655]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb2916f7ed052fb9413","sql":"SELECT * FROM `business_payment_transactions` WHERE `status` = ? and `type` = ?, [2 DISBURSEMENT]","duration":"80.6976ms","duration_ms":80}
{"level":"dev.info","ts":"[2025-08-27 09:33:06.655]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb2916f7ed052fb9413","sql":"\n\t\tSELECT\n\t\t\tDATE(due_date) as date,\n\t\t\tCOALESCE(SUM(total_due_amount), 0) as due_amount\n\t\tFROM business_repayment_bills\n\t\tWHERE due_date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)\n\t\t\tAND due_date < CURDATE()\n\t\tGROUP BY DATE(due_date)\n\t\tORDER BY date\n\t, [7]","duration":"79.9663ms","duration_ms":79}
{"level":"dev.info","ts":"[2025-08-27 09:33:06.655]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb2916f7ed052fb9413","sql":"\n\t\tSELECT\n\t\t\tDATE(FROM_UNIXTIME(createtime)) as date,\n\t\t\tCOUNT(*) as registration_count\n\t\tFROM business_app_account\n\t\tWHERE createtime >= UNIX_TIMESTAMP(DATE_SUB(CURDATE(), INTERVAL ? DAY))\n\t\t\tAND createtime < UNIX_TIMESTAMP(CURDATE())\n\t\tGROUP BY DATE(FROM_UNIXTIME(createtime))\n\t\tORDER BY date\n\t, [7]","duration":"80.5366ms","duration_ms":80}
{"level":"dev.info","ts":"[2025-08-27 09:33:06.655]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb2916f7ed052fb9413","sql":"\n\t\tSELECT\n\t\t\tDATE(due_date) as date,\n\t\t\tCOALESCE(SUM(paid_amount), 0) as repayment_amount\n\t\tFROM business_repayment_bills\n\t\tWHERE paid_amount > 0\n\t\t\tAND due_date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)\n\t\t\tAND due_date < CURDATE()\n\t\tGROUP BY DATE(due_date)\n\t\tORDER BY date\n\t, [7]","duration":"79.9663ms","duration_ms":79}
{"level":"dev.info","ts":"[2025-08-27 09:33:06.655]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb2916f7ed052fb9413","sql":"SELECT COALESCE(SUM(total_due_amount - total_waive_amount), 0) as due_amount,COALESCE(SUM(due_principal), 0) as due_principal_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [2025-08-27 2025-08-27]","duration":"80.5366ms","duration_ms":80}
{"level":"dev.info","ts":"[2025-08-27 09:33:06.655]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb2916f7ed052fb9413","sql":"\n\t\tSELECT\n\t\t\tDATE(completed_at) as date,\n\t\t\tCOALESCE(SUM(amount), 0) as disbursement_amount\n\t\tFROM business_payment_transactions\n\t\tWHERE status = ?\n\t\t\tAND type = ?\n\t\t\tAND DATE(completed_at) >= DATE_SUB(CURDATE(), INTERVAL ? DAY)\n\t\t\tAND DATE(completed_at) < CURDATE()\n\t\tGROUP BY DATE(completed_at)\n\t\tORDER BY date\n\t, [2 DISBURSEMENT 7]","duration":"79.9663ms","duration_ms":79}
{"level":"dev.info","ts":"[2025-08-27 09:33:06.655]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb2916f7ed052fb9413","sql":"SELECT COALESCE(SUM(total_due_amount - total_waive_amount), 0) as due_amount,COALESCE(SUM(due_principal), 0) as due_principal_amount FROM `business_repayment_bills` WHERE DATE(due_date) <= ? LIMIT 1, [2025-08-27]","duration":"79.9663ms","duration_ms":79}
{"level":"dev.info","ts":"[2025-08-27 09:33:06.665]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb2916f7ed052fb9413","sql":"SELECT COALESCE(SUM(amount), 0) as total_amount, COUNT(*) as order_count FROM `business_payment_transactions` WHERE `status` = ? and `type` = ? and DATE(completed_at) BETWEEN ? and ? LIMIT 1, [2 DISBURSEMENT 2025-08-27 2025-08-27]","duration":"16.7021ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-27 09:33:06.671]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb2916f7ed052fb9413","sql":"SELECT COALESCE(SUM(amount), 0) as total_amount, COUNT(*) as order_count FROM `business_payment_transactions` WHERE `status` = ? and `type` = ? LIMIT 1, [2 DISBURSEMENT]","duration":"16.5102ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-27 09:33:06.672]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb2916f7ed052fb9413","sql":"SELECT COALESCE(SUM(paid_amount), 0) as repayment_amount FROM `business_repayment_bills` WHERE DATE(due_date) <= ? LIMIT 1, [2025-08-27]","duration":"16.4833ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-27 09:33:06.672]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb2916f7ed052fb9413","sql":"SELECT COALESCE(SUM(paid_amount), 0) as repayment_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [2025-08-27 2025-08-27]","duration":"16.4833ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-27 09:33:06.688]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb2916f7ed052fb9413","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_payment_transactions` WHERE `status` = ? and `type` = ? and DATE(completed_at) BETWEEN ? and ? LIMIT 1, [2 DISBURSEMENT 2025-08-27 2025-08-27]","duration":"22.3713ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-27 09:33:06.699]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb29167c258f755d3d6","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_payment_transactions` WHERE `status` = ? and `type` = ? LIMIT 1, [2 DISBURSEMENT]","duration":"27.6598ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-08-27 09:33:06.735]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb29167c25809bd7a09","sql":"SELECT COALESCE(SUM(principal), 0) as principal_amount FROM `business_loan_orders` WHERE DATE(disbursed_at) BETWEEN ? and ? LIMIT 1, [2025-08-27 2025-08-27]","duration":"46.6521ms","duration_ms":46}
{"level":"dev.info","ts":"[2025-08-27 09:33:06.751]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb29167c25809bd7a09","sql":"SELECT COALESCE(SUM(principal), 0) as principal_amount FROM `business_loan_orders` WHERE `id` IN (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) LIMIT 1, [60 61 62 63 64 65 66 67 68 69 70 73 75 76 77 78 79 80 81 82 83 84 85 86 87 89 88]","duration":"50.6613ms","duration_ms":50}
{"level":"dev.info","ts":"[2025-08-27 09:34:26.188]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb29167c25809bd7a09","sql":"SELECT id,accountID,businessID,password,salt,name,username FROM `business_account` WHERE (`username` = ? or `email` = ?) and `mobile` = ? LIMIT 1, [fincore fincore ***********]","duration":"54.032ms","duration_ms":54}
{"level":"dev.info","ts":"[2025-08-27 09:34:34.278]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb29167c25809bd7a09","sql":"UPDATE `business_account` SET `lastLoginTime` = ?,`lastLoginIp` = ?,`loginstatus` = ? WHERE `id` = ?, [**********  1 1]","duration":"30.6237ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-08-27 09:34:34.295]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb29167c25809bd7a09","sql":"SELECT * FROM `business_auth_role_access` WHERE `uid` = ?, [1]","duration":"16.3441ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-27 09:34:34.331]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb29167c25809bd7a09","sql":"SELECT * FROM `business_auth_role` WHERE `id` IN (?), [1]","duration":"35.9476ms","duration_ms":35}
{"level":"dev.info","ts":"[2025-08-27 09:34:34.359]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb29167c25809bd7a09","sql":"INSERT INTO `admin_operate_log` (`detail`,`ip`,`region`,`uid`,`operate_name`,`created_at`,`result`) VALUES (?,?,?,?,?,?,?), [成功  未知 1 登录 2025-08-27 09:34:34.3312455 +0800 CST m=+127.********* 成功]","duration":"27.6536ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-08-27 09:34:34.388]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb29167c25809bd7a09","sql":"SELECT id,businessID,username,name,nickname,city,company,avatar,status FROM `business_account` WHERE `id` = ? LIMIT 1, [1]","duration":"14.0823ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-27 09:34:34.417]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb29167c25809bd7a09","sql":"SELECT * FROM `common_config` WHERE `keyname` = ? LIMIT 1, [rooturl]","duration":"29.1691ms","duration_ms":29}
{"level":"dev.info","ts":"[2025-08-27 09:34:39.574]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb29167c25809bd7a09","sql":"SELECT * FROM `business_auth_role_access` WHERE `uid` = ?, [1]","duration":"5.199956s","duration_ms":5199}
{"level":"dev.info","ts":"[2025-08-27 09:34:39.592]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb29167c25809bd7a09","sql":"SELECT * FROM `business_auth_role` WHERE `id` IN (?), [1]","duration":"16.1353ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-27 09:34:39.637]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb29167c25809bd7a09","sql":"SELECT * FROM `business_auth_rule` WHERE `status` = ? and `type` IN (?,?) ORDER BY orderNo asc, [0 0 1]","duration":"44.5555ms","duration_ms":44}
{"level":"dev.info","ts":"[2025-08-27 09:34:39.676]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb29167c25809bd7a09","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 8]","duration":"39.4752ms","duration_ms":39}
{"level":"dev.info","ts":"[2025-08-27 09:34:39.696]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb29167c25809bd7a09","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 70]","duration":"20.0476ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-27 09:34:39.720]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb29167c25809bd7a09","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 69]","duration":"23.6698ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-27 09:34:39.745]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb29167c25809bd7a09","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 68]","duration":"25.1126ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-08-27 09:34:39.770]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb29167c25809bd7a09","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 463]","duration":"24.7526ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-08-27 09:34:39.802]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb29167c25809bd7a09","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 460]","duration":"32.3818ms","duration_ms":32}
{"level":"dev.info","ts":"[2025-08-27 09:34:39.830]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb29167c25809bd7a09","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 451]","duration":"27.705ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-08-27 09:34:39.880]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb29167c25809bd7a09","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 458]","duration":"50.0075ms","duration_ms":50}
{"level":"dev.info","ts":"[2025-08-27 09:34:39.898]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb29167c25809bd7a09","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 450]","duration":"18.2793ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-27 09:34:39.914]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb29167c25809bd7a09","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 457]","duration":"15.4518ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-27 09:34:39.976]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb29167c25809bd7a09","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 456]","duration":"62.1497ms","duration_ms":62}
{"level":"dev.info","ts":"[2025-08-27 09:34:39.997]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb29167c25809bd7a09","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 464]","duration":"21.5376ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-27 09:34:40.066]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb29167c25809bd7a09","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 465]","duration":"67.5661ms","duration_ms":67}
{"level":"dev.info","ts":"[2025-08-27 09:34:40.089]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb29167c25809bd7a09","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 455]","duration":"22.8421ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-27 09:34:40.107]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb29167c25809bd7a09","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 440]","duration":"17.3174ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-27 09:34:40.167]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb29167c25809bd7a09","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 439]","duration":"59.6714ms","duration_ms":59}
{"level":"dev.info","ts":"[2025-08-27 09:34:40.207]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb29167c25809bd7a09","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 467]","duration":"39.5328ms","duration_ms":39}
{"level":"dev.info","ts":"[2025-08-27 09:34:40.243]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb29167c25809bd7a09","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 468]","duration":"35.5791ms","duration_ms":35}
{"level":"dev.info","ts":"[2025-08-27 09:34:40.280]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb29167c25809bd7a09","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 469]","duration":"37.756ms","duration_ms":37}
{"level":"dev.info","ts":"[2025-08-27 09:34:40.307]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb29167c25809bd7a09","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 466]","duration":"26.1476ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-08-27 09:34:40.334]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb29167c25809bd7a09","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 453]","duration":"27.0977ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-08-27 09:34:40.379]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb29167c25809bd7a09","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 459]","duration":"45.1874ms","duration_ms":45}
{"level":"dev.info","ts":"[2025-08-27 09:34:40.397]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb29167c25809bd7a09","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 452]","duration":"17.9982ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-27 09:34:40.421]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb29167c25809bd7a09","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 438]","duration":"23.2259ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-27 09:34:40.441]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb29167c25809bd7a09","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 437]","duration":"20.24ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-27 09:34:40.474]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb29167c25809bd7a09","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 435]","duration":"30.7239ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-08-27 09:34:40.512]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb29167c25809bd7a09","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 436]","duration":"38.5477ms","duration_ms":38}
{"level":"dev.info","ts":"[2025-08-27 09:34:40.576]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb29167c25809bd7a09","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 383]","duration":"63.6348ms","duration_ms":63}
{"level":"dev.info","ts":"[2025-08-27 09:34:40.602]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb29167c25809bd7a09","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 75]","duration":"26.3341ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-08-27 09:34:40.621]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb29167c25809bd7a09","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 97]","duration":"19.3494ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-27 09:34:40.643]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb29167c25809bd7a09","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 374]","duration":"21.1461ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-27 09:34:40.658]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb29167c25809bd7a09","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 74]","duration":"15.8176ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-27 09:34:40.686]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb29167c25809bd7a09","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 11]","duration":"27.3647ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-08-27 09:34:40.712]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb29167c25809bd7a09","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 12]","duration":"26.1482ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-08-27 09:34:40.752]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb29167c25809bd7a09","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 63]","duration":"39.2935ms","duration_ms":39}
{"level":"dev.info","ts":"[2025-08-27 09:34:40.772]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb29167c25809bd7a09","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 13]","duration":"20.3875ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-27 09:34:40.828]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bb29167c25809bd7a09","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 61]","duration":"54.7579ms","duration_ms":54}
{"level":"dev.info","ts":"[2025-08-27 09:34:44.773]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-27 3 9 2]","duration":"50.6645ms","duration_ms":50}
{"level":"dev.info","ts":"[2025-08-27 09:34:44.773]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"\n\t\tSELECT\n\t\t\tDATE(FROM_UNIXTIME(createtime)) as date,\n\t\t\tCOUNT(*) as registration_count\n\t\tFROM business_app_account\n\t\tWHERE createtime >= UNIX_TIMESTAMP(DATE_SUB(CURDATE(), INTERVAL ? DAY))\n\t\t\tAND createtime < UNIX_TIMESTAMP(CURDATE())\n\t\tGROUP BY DATE(FROM_UNIXTIME(createtime))\n\t\tORDER BY date\n\t, [7]","duration":"51.1753ms","duration_ms":51}
{"level":"dev.info","ts":"[2025-08-27 09:34:44.805]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"SELECT COALESCE(SUM(total_due_amount - paid_amount - total_waive_amount), 0) as overdue_amount FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-27 3 9 2]","duration":"32.072ms","duration_ms":32}
{"level":"dev.info","ts":"[2025-08-27 09:34:44.828]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"SELECT COALESCE(SUM(total_due_amount - total_waive_amount), 0) as due_amount,COALESCE(SUM(due_principal), 0) as due_principal_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [2025-08-27 2025-08-27]","duration":"105.178ms","duration_ms":105}
{"level":"dev.info","ts":"[2025-08-27 09:34:44.828]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"SELECT COALESCE(SUM(total_due_amount - total_waive_amount), 0) as due_amount,COALESCE(SUM(due_principal), 0) as due_principal_amount FROM `business_repayment_bills` WHERE DATE(due_date) <= ? LIMIT 1, [2025-08-27]","duration":"104.6541ms","duration_ms":104}
{"level":"dev.info","ts":"[2025-08-27 09:34:44.828]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"\n\t\tSELECT\n\t\t\tDATE(due_date) as date,\n\t\t\tCOALESCE(SUM(total_due_amount), 0) as due_amount\n\t\tFROM business_repayment_bills\n\t\tWHERE due_date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)\n\t\t\tAND due_date < CURDATE()\n\t\tGROUP BY DATE(due_date)\n\t\tORDER BY date\n\t, [7]","duration":"105.178ms","duration_ms":105}
{"level":"dev.info","ts":"[2025-08-27 09:34:44.828]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"\n\t\tSELECT\n\t\t\tDATE(completed_at) as date,\n\t\t\tCOALESCE(SUM(amount), 0) as disbursement_amount\n\t\tFROM business_payment_transactions\n\t\tWHERE status = ?\n\t\t\tAND type = ?\n\t\t\tAND DATE(completed_at) >= DATE_SUB(CURDATE(), INTERVAL ? DAY)\n\t\t\tAND DATE(completed_at) < CURDATE()\n\t\tGROUP BY DATE(completed_at)\n\t\tORDER BY date\n\t, [2 DISBURSEMENT 7]","duration":"105.6994ms","duration_ms":105}
{"level":"dev.info","ts":"[2025-08-27 09:34:44.828]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"\n\t\tSELECT\n\t\t\tDATE(due_date) as date,\n\t\t\tCOALESCE(SUM(paid_amount), 0) as repayment_amount\n\t\tFROM business_repayment_bills\n\t\tWHERE paid_amount > 0\n\t\t\tAND due_date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)\n\t\t\tAND due_date < CURDATE()\n\t\tGROUP BY DATE(due_date)\n\t\tORDER BY date\n\t, [7]","duration":"105.178ms","duration_ms":105}
{"level":"dev.info","ts":"[2025-08-27 09:34:44.862]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-27 3 9 2]","duration":"139.6069ms","duration_ms":139}
{"level":"dev.info","ts":"[2025-08-27 09:34:44.862]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"SELECT * FROM `business_payment_transactions` WHERE `status` = ? and `type` = ? and DATE(completed_at) BETWEEN ? and ?, [2 DISBURSEMENT 2025-08-27 2025-08-27]","duration":"139.1644ms","duration_ms":139}
{"level":"dev.info","ts":"[2025-08-27 09:34:44.862]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"SELECT * FROM `business_payment_transactions` WHERE `status` = ? and `type` = ?, [2 DISBURSEMENT]","duration":"139.1644ms","duration_ms":139}
{"level":"dev.info","ts":"[2025-08-27 09:34:44.862]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"SELECT COALESCE(SUM(paid_amount), 0) as repayment_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [2025-08-27 2025-08-27]","duration":"33.9864ms","duration_ms":33}
{"level":"dev.info","ts":"[2025-08-27 09:34:48.336]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"SELECT COALESCE(SUM(paid_amount), 0) as repayment_amount FROM `business_repayment_bills` WHERE DATE(due_date) <= ? LIMIT 1, [2025-08-27]","duration":"3.5081837s","duration_ms":3508}
{"level":"dev.info","ts":"[2025-08-27 09:34:48.368]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"SELECT COALESCE(SUM(amount), 0) as total_amount, COUNT(*) as order_count FROM `business_payment_transactions` WHERE `status` = ? and `type` = ? and DATE(completed_at) BETWEEN ? and ? LIMIT 1, [2 DISBURSEMENT 2025-08-27 2025-08-27]","duration":"3.5058956s","duration_ms":3505}
{"level":"dev.info","ts":"[2025-08-27 09:34:48.369]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"SELECT COALESCE(SUM(amount), 0) as total_amount, COUNT(*) as order_count FROM `business_payment_transactions` WHERE `status` = ? and `type` = ? LIMIT 1, [2 DISBURSEMENT]","duration":"3.5068951s","duration_ms":3506}
{"level":"dev.info","ts":"[2025-08-27 09:34:48.369]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"SELECT COALESCE(SUM(total_due_amount - paid_amount - total_waive_amount), 0) as overdue_amount FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-27 3 9 2]","duration":"3.5068951s","duration_ms":3506}
{"level":"dev.info","ts":"[2025-08-27 09:34:48.396]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_payment_transactions` WHERE `status` = ? and `type` = ? and DATE(completed_at) BETWEEN ? and ? LIMIT 1, [2 DISBURSEMENT 2025-08-27 2025-08-27]","duration":"26.8276ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-08-27 09:34:48.396]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44304ecf26","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_payment_transactions` WHERE `status` = ? and `type` = ? LIMIT 1, [2 DISBURSEMENT]","duration":"26.8276ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-08-27 09:34:48.417]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"SELECT COALESCE(SUM(principal), 0) as principal_amount FROM `business_loan_orders` WHERE DATE(disbursed_at) BETWEEN ? and ? LIMIT 1, [2025-08-27 2025-08-27]","duration":"21.0633ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-27 09:34:48.417]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"SELECT COALESCE(SUM(principal), 0) as principal_amount FROM `business_loan_orders` WHERE `id` IN (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) LIMIT 1, [60 61 62 63 64 65 66 67 68 69 70 73 75 76 77 78 79 80 81 82 83 84 85 86 87 89 88]","duration":"20.5574ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-27 09:35:30.143]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"SELECT id,accountID,businessID,password,salt,name,username FROM `business_account` WHERE (`username` = ? or `email` = ?) and `mobile` = ? LIMIT 1, [fincore fincore ***********]","duration":"180.8673ms","duration_ms":180}
{"level":"dev.info","ts":"[2025-08-27 09:35:30.195]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"UPDATE `business_account` SET `loginstatus` = ?,`lastLoginTime` = ?,`lastLoginIp` = ? WHERE `id` = ?, [1 **********  1]","duration":"42.8663ms","duration_ms":42}
{"level":"dev.info","ts":"[2025-08-27 09:35:30.223]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"SELECT * FROM `business_auth_role_access` WHERE `uid` = ?, [1]","duration":"28.3108ms","duration_ms":28}
{"level":"dev.info","ts":"[2025-08-27 09:35:30.276]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"SELECT * FROM `business_auth_role` WHERE `id` IN (?), [1]","duration":"52.2697ms","duration_ms":52}
{"level":"dev.info","ts":"[2025-08-27 09:35:30.324]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"INSERT INTO `admin_operate_log` (`result`,`detail`,`ip`,`region`,`uid`,`operate_name`,`created_at`) VALUES (?,?,?,?,?,?,?), [成功 成功  未知 1 登录 2025-08-27 09:35:30.2772891 +0800 CST m=+183.882463001]","duration":"47.1178ms","duration_ms":47}
{"level":"dev.info","ts":"[2025-08-27 09:35:30.399]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"SELECT * FROM `business_auth_role_access` WHERE `uid` = ?, [1]","duration":"50.2001ms","duration_ms":50}
{"level":"dev.info","ts":"[2025-08-27 09:35:30.400]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"SELECT id,businessID,username,name,nickname,city,company,avatar,status FROM `business_account` WHERE `id` = ? LIMIT 1, [1]","duration":"50.7469ms","duration_ms":50}
{"level":"dev.info","ts":"[2025-08-27 09:35:30.437]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"SELECT * FROM `common_config` WHERE `keyname` = ? LIMIT 1, [rooturl]","duration":"37.9296ms","duration_ms":37}
{"level":"dev.info","ts":"[2025-08-27 09:35:30.457]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"SELECT * FROM `business_auth_role` WHERE `id` IN (?), [1]","duration":"57.1776ms","duration_ms":57}
{"level":"dev.info","ts":"[2025-08-27 09:35:30.483]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"SELECT * FROM `business_auth_rule` WHERE `status` = ? and `type` IN (?,?) ORDER BY orderNo asc, [0 0 1]","duration":"25.885ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-08-27 09:35:30.520]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 8]","duration":"36.6792ms","duration_ms":36}
{"level":"dev.info","ts":"[2025-08-27 09:35:30.539]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 70]","duration":"18.5117ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-27 09:35:30.559]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 69]","duration":"19.7829ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-27 09:35:30.578]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 68]","duration":"18.3955ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-27 09:35:30.600]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 463]","duration":"22.7036ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-27 09:35:30.648]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 460]","duration":"47.4399ms","duration_ms":47}
{"level":"dev.info","ts":"[2025-08-27 09:35:30.686]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 451]","duration":"37.8893ms","duration_ms":37}
{"level":"dev.info","ts":"[2025-08-27 09:35:30.720]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 458]","duration":"34.0779ms","duration_ms":34}
{"level":"dev.info","ts":"[2025-08-27 09:35:30.767]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 450]","duration":"46.325ms","duration_ms":46}
{"level":"dev.info","ts":"[2025-08-27 09:35:30.794]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 457]","duration":"27.4805ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-08-27 09:35:30.827]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 456]","duration":"33.1691ms","duration_ms":33}
{"level":"dev.info","ts":"[2025-08-27 09:35:30.845]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 464]","duration":"17.79ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-27 09:35:30.867]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 465]","duration":"21.8788ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-27 09:35:30.885]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 455]","duration":"17.4641ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-27 09:35:30.905]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 440]","duration":"20.1269ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-27 09:35:30.954]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 439]","duration":"49.5019ms","duration_ms":49}
{"level":"dev.info","ts":"[2025-08-27 09:35:30.973]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 467]","duration":"17.4269ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-27 09:35:30.992]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 468]","duration":"18.8715ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-27 09:35:31.010]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 469]","duration":"18.3062ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-27 09:35:31.030]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 466]","duration":"19.4897ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-27 09:35:31.059]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 453]","duration":"28.6351ms","duration_ms":28}
{"level":"dev.info","ts":"[2025-08-27 09:35:31.112]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 459]","duration":"52.6527ms","duration_ms":52}
{"level":"dev.info","ts":"[2025-08-27 09:35:31.144]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 452]","duration":"31.6424ms","duration_ms":31}
{"level":"dev.info","ts":"[2025-08-27 09:35:31.177]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 438]","duration":"33.3377ms","duration_ms":33}
{"level":"dev.info","ts":"[2025-08-27 09:35:31.195]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 437]","duration":"16.9652ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-27 09:35:31.216]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 435]","duration":"20.7076ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-27 09:35:31.236]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 436]","duration":"20.1593ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-27 09:35:31.270]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 383]","duration":"33.6396ms","duration_ms":33}
{"level":"dev.info","ts":"[2025-08-27 09:35:31.301]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 75]","duration":"31.2497ms","duration_ms":31}
{"level":"dev.info","ts":"[2025-08-27 09:35:31.356]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 97]","duration":"54.9142ms","duration_ms":54}
{"level":"dev.info","ts":"[2025-08-27 09:35:31.377]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 374]","duration":"20.8771ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-27 09:35:31.399]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 74]","duration":"21.6826ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-27 09:35:31.463]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 11]","duration":"63.8215ms","duration_ms":63}
{"level":"dev.info","ts":"[2025-08-27 09:35:31.514]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 12]","duration":"50.6188ms","duration_ms":50}
{"level":"dev.info","ts":"[2025-08-27 09:35:31.532]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 63]","duration":"18.0403ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-27 09:35:31.565]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 13]","duration":"32.8232ms","duration_ms":32}
{"level":"dev.info","ts":"[2025-08-27 09:35:31.611]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bc96b9fcb44c9cc05ca","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 61]","duration":"44.7769ms","duration_ms":44}
{"level":"dev.info","ts":"[2025-08-27 09:35:31.772]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bd45f0c5a1801972ccc","sql":"SELECT COALESCE(SUM(total_due_amount - total_waive_amount), 0) as due_amount,COALESCE(SUM(due_principal), 0) as due_principal_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [2025-08-27 2025-08-27]","duration":"19.2011ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-27 09:35:31.772]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bd45f0c5a1801972ccc","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-27 3 9 2]","duration":"18.6744ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-27 09:35:31.780]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bd45f0c5a1801972ccc","sql":"\n\t\tSELECT\n\t\t\tDATE(due_date) as date,\n\t\t\tCOALESCE(SUM(total_due_amount), 0) as due_amount\n\t\tFROM business_repayment_bills\n\t\tWHERE due_date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)\n\t\t\tAND due_date < CURDATE()\n\t\tGROUP BY DATE(due_date)\n\t\tORDER BY date\n\t, [7]","duration":"23.2412ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-27 09:35:31.780]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bd45f0c5a1801972ccc","sql":"SELECT * FROM `business_payment_transactions` WHERE `status` = ? and `type` = ? and DATE(completed_at) BETWEEN ? and ?, [2 DISBURSEMENT 2025-08-27 2025-08-27]","duration":"26.9009ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-08-27 09:35:31.797]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bd45f0c5a1801972ccc","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-27 3 9 2]","duration":"39.8682ms","duration_ms":39}
{"level":"dev.info","ts":"[2025-08-27 09:35:31.797]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bd45f0c5a1801972ccc","sql":"SELECT COALESCE(SUM(paid_amount), 0) as repayment_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [2025-08-27 2025-08-27]","duration":"24.347ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-08-27 09:35:31.797]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bd45f0c5a1801972ccc","sql":"\n\t\tSELECT\n\t\t\tDATE(FROM_UNIXTIME(createtime)) as date,\n\t\t\tCOUNT(*) as registration_count\n\t\tFROM business_app_account\n\t\tWHERE createtime >= UNIX_TIMESTAMP(DATE_SUB(CURDATE(), INTERVAL ? DAY))\n\t\t\tAND createtime < UNIX_TIMESTAMP(CURDATE())\n\t\tGROUP BY DATE(FROM_UNIXTIME(createtime))\n\t\tORDER BY date\n\t, [7]","duration":"41.4803ms","duration_ms":41}
{"level":"dev.info","ts":"[2025-08-27 09:35:31.797]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bd45f0c5a1801972ccc","sql":"\n\t\tSELECT\n\t\t\tDATE(completed_at) as date,\n\t\t\tCOALESCE(SUM(amount), 0) as disbursement_amount\n\t\tFROM business_payment_transactions\n\t\tWHERE status = ?\n\t\t\tAND type = ?\n\t\t\tAND DATE(completed_at) >= DATE_SUB(CURDATE(), INTERVAL ? DAY)\n\t\t\tAND DATE(completed_at) < CURDATE()\n\t\tGROUP BY DATE(completed_at)\n\t\tORDER BY date\n\t, [2 DISBURSEMENT 7]","duration":"40.9458ms","duration_ms":40}
{"level":"dev.info","ts":"[2025-08-27 09:35:31.797]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bd45f0c5a1801972ccc","sql":"\n\t\tSELECT\n\t\t\tDATE(due_date) as date,\n\t\t\tCOALESCE(SUM(paid_amount), 0) as repayment_amount\n\t\tFROM business_repayment_bills\n\t\tWHERE paid_amount > 0\n\t\t\tAND due_date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)\n\t\t\tAND due_date < CURDATE()\n\t\tGROUP BY DATE(due_date)\n\t\tORDER BY date\n\t, [7]","duration":"40.9458ms","duration_ms":40}
{"level":"dev.info","ts":"[2025-08-27 09:35:31.798]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bd45f0c5a1801972ccc","sql":"SELECT COALESCE(SUM(total_due_amount - paid_amount - total_waive_amount), 0) as overdue_amount FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-27 3 9 2]","duration":"25.4179ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-08-27 09:35:31.831]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bd45f0c5a1801972ccc","sql":"SELECT COALESCE(SUM(amount), 0) as total_amount, COUNT(*) as order_count FROM `business_payment_transactions` WHERE `status` = ? and `type` = ? and DATE(completed_at) BETWEEN ? and ? LIMIT 1, [2 DISBURSEMENT 2025-08-27 2025-08-27]","duration":"51.0582ms","duration_ms":51}
{"level":"dev.info","ts":"[2025-08-27 09:35:31.832]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bd45f0c5a1801972ccc","sql":"SELECT * FROM `business_payment_transactions` WHERE `status` = ? and `type` = ?, [2 DISBURSEMENT]","duration":"75.8658ms","duration_ms":75}
{"level":"dev.info","ts":"[2025-08-27 09:35:31.860]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bd45f0c5a1801972ccc","sql":"SELECT COALESCE(SUM(total_due_amount - paid_amount - total_waive_amount), 0) as overdue_amount FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-27 3 9 2]","duration":"61.462ms","duration_ms":61}
{"level":"dev.info","ts":"[2025-08-27 09:35:31.869]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bd45f0c5a1801972ccc","sql":"SELECT COALESCE(SUM(amount), 0) as total_amount, COUNT(*) as order_count FROM `business_payment_transactions` WHERE `status` = ? and `type` = ? LIMIT 1, [2 DISBURSEMENT]","duration":"36.5295ms","duration_ms":36}
{"level":"dev.info","ts":"[2025-08-27 09:35:31.870]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bd45f0c5a1801972ccc","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_payment_transactions` WHERE `status` = ? and `type` = ? and DATE(completed_at) BETWEEN ? and ? LIMIT 1, [2 DISBURSEMENT 2025-08-27 2025-08-27]","duration":"38.6606ms","duration_ms":38}
{"level":"dev.info","ts":"[2025-08-27 09:35:31.893]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bd45edccf64e9437842","sql":"SELECT COALESCE(SUM(total_due_amount - total_waive_amount), 0) as due_amount,COALESCE(SUM(due_principal), 0) as due_principal_amount FROM `business_repayment_bills` WHERE DATE(due_date) <= ? LIMIT 1, [2025-08-27]","duration":"136.2241ms","duration_ms":136}
{"level":"dev.info","ts":"[2025-08-27 09:35:31.900]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bd45edccf64e9437842","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_payment_transactions` WHERE `status` = ? and `type` = ? LIMIT 1, [2 DISBURSEMENT]","duration":"30.7584ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-08-27 09:35:31.900]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bd45edccf64e9437842","sql":"SELECT COALESCE(SUM(principal), 0) as principal_amount FROM `business_loan_orders` WHERE DATE(disbursed_at) BETWEEN ? and ? LIMIT 1, [2025-08-27 2025-08-27]","duration":"29.711ms","duration_ms":29}
{"level":"dev.info","ts":"[2025-08-27 09:35:31.909]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bd45f0c5a1801972ccc","sql":"SELECT COALESCE(SUM(paid_amount), 0) as repayment_amount FROM `business_repayment_bills` WHERE DATE(due_date) <= ? LIMIT 1, [2025-08-27]","duration":"15.9308ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-27 09:35:31.928]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bd45f0c5a1801972ccc","sql":"SELECT COALESCE(SUM(principal), 0) as principal_amount FROM `business_loan_orders` WHERE `id` IN (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) LIMIT 1, [60 61 62 63 64 65 66 67 68 69 70 73 75 76 77 78 79 80 81 82 83 84 85 86 87 89 88]","duration":"27.2931ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-08-27 09:36:28.314]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT id,accountID,businessID,password,salt,name,username FROM `business_account` WHERE (`username` = ? or `email` = ?) and `mobile` = ? LIMIT 1, [fincore fincore ***********]","duration":"21.7782ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-27 09:36:28.383]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `business_account` SET `lastLoginTime` = ?,`lastLoginIp` = ?,`loginstatus` = ? WHERE `id` = ?, [**********  1 1]","duration":"39.365ms","duration_ms":39}
{"level":"dev.info","ts":"[2025-08-27 09:36:28.402]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_role_access` WHERE `uid` = ?, [1]","duration":"18.8034ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-27 09:36:28.439]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_role` WHERE `id` IN (?), [1]","duration":"37.5194ms","duration_ms":37}
{"level":"dev.info","ts":"[2025-08-27 09:36:28.468]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"INSERT INTO `admin_operate_log` (`ip`,`region`,`uid`,`operate_name`,`created_at`,`result`,`detail`) VALUES (?,?,?,?,?,?,?), [ 未知 1 登录 2025-08-27 09:36:28.4399693 +0800 CST m=+18.********* 成功 成功]","duration":"28.5478ms","duration_ms":28}
{"level":"dev.info","ts":"[2025-08-27 09:36:28.513]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_role_access` WHERE `uid` = ?, [1]","duration":"27.3144ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-08-27 09:36:28.558]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_role` WHERE `id` IN (?), [1]","duration":"44.7771ms","duration_ms":44}
{"level":"dev.info","ts":"[2025-08-27 09:36:28.573]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT id,businessID,username,name,nickname,city,company,avatar,status FROM `business_account` WHERE `id` = ? LIMIT 1, [1]","duration":"87.2047ms","duration_ms":87}
{"level":"dev.info","ts":"[2025-08-27 09:36:28.614]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `status` = ? and `type` IN (?,?) ORDER BY orderNo asc, [0 0 1]","duration":"55.4724ms","duration_ms":55}
{"level":"dev.info","ts":"[2025-08-27 09:36:28.632]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `common_config` WHERE `keyname` = ? LIMIT 1, [rooturl]","duration":"58.335ms","duration_ms":58}
{"level":"dev.info","ts":"[2025-08-27 09:36:28.647]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 8]","duration":"31.6223ms","duration_ms":31}
{"level":"dev.info","ts":"[2025-08-27 09:36:28.694]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 70]","duration":"47.2058ms","duration_ms":47}
{"level":"dev.info","ts":"[2025-08-27 09:36:28.748]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 69]","duration":"53.3809ms","duration_ms":53}
{"level":"dev.info","ts":"[2025-08-27 09:36:28.784]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 68]","duration":"36.0761ms","duration_ms":36}
{"level":"dev.info","ts":"[2025-08-27 09:36:28.802]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 463]","duration":"17.663ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-27 09:36:28.836]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 460]","duration":"34.4985ms","duration_ms":34}
{"level":"dev.info","ts":"[2025-08-27 09:36:28.854]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 451]","duration":"17.854ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-27 09:36:28.874]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 458]","duration":"19.5272ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-27 09:36:28.901]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 450]","duration":"23.6057ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-27 09:36:28.918]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 457]","duration":"17.3179ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-27 09:36:28.968]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 456]","duration":"48.7106ms","duration_ms":48}
{"level":"dev.info","ts":"[2025-08-27 09:36:29.014]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 464]","duration":"46.057ms","duration_ms":46}
{"level":"dev.info","ts":"[2025-08-27 09:36:29.034]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 465]","duration":"19.7655ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-27 09:36:29.052]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 455]","duration":"18.8532ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-27 09:36:29.074]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 440]","duration":"21.8388ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-27 09:36:29.089]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 439]","duration":"15.1856ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-27 09:36:29.108]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 467]","duration":"17.4854ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-27 09:36:29.186]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 468]","duration":"77.4146ms","duration_ms":77}
{"level":"dev.info","ts":"[2025-08-27 09:36:29.200]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 469]","duration":"14.3166ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-27 09:36:29.220]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 466]","duration":"20.2031ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-27 09:36:29.284]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 453]","duration":"63.7752ms","duration_ms":63}
{"level":"dev.info","ts":"[2025-08-27 09:36:29.313]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 459]","duration":"28.4674ms","duration_ms":28}
{"level":"dev.info","ts":"[2025-08-27 09:36:29.336]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 452]","duration":"23.2064ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-27 09:36:29.371]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 438]","duration":"34.7521ms","duration_ms":34}
{"level":"dev.info","ts":"[2025-08-27 09:36:29.394]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 437]","duration":"22.622ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-27 09:36:29.448]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 435]","duration":"54.3901ms","duration_ms":54}
{"level":"dev.info","ts":"[2025-08-27 09:36:29.479]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 436]","duration":"30.5434ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-08-27 09:36:29.521]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 383]","duration":"42.1037ms","duration_ms":42}
{"level":"dev.info","ts":"[2025-08-27 09:36:29.544]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 75]","duration":"22.9696ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-27 09:36:29.574]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 97]","duration":"30.4117ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-08-27 09:36:29.593]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 374]","duration":"19.2739ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-27 09:36:29.627]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 74]","duration":"33.872ms","duration_ms":33}
{"level":"dev.info","ts":"[2025-08-27 09:36:29.649]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 11]","duration":"21.2456ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-27 09:36:29.676]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 12]","duration":"27.4446ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-08-27 09:36:29.711]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 63]","duration":"34.6301ms","duration_ms":34}
{"level":"dev.info","ts":"[2025-08-27 09:36:29.730]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 13]","duration":"18.5226ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-27 09:36:29.750]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 61]","duration":"20.23ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-27 09:36:29.910]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7be1e79a9d8853ce3a26","sql":"SELECT * FROM `business_payment_transactions` WHERE `status` = ? and `type` = ? and DATE(completed_at) BETWEEN ? and ?, [2 DISBURSEMENT 2025-08-27 2025-08-27]","duration":"28.2419ms","duration_ms":28}
{"level":"dev.info","ts":"[2025-08-27 09:36:29.911]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7be1e79a9d8853ce3a26","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-27 3 9 2]","duration":"29.5193ms","duration_ms":29}
{"level":"dev.info","ts":"[2025-08-27 09:36:29.930]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7be1e79a9d8853ce3a26","sql":"SELECT COALESCE(SUM(amount), 0) as total_amount, COUNT(*) as order_count FROM `business_payment_transactions` WHERE `status` = ? and `type` = ? and DATE(completed_at) BETWEEN ? and ? LIMIT 1, [2 DISBURSEMENT 2025-08-27 2025-08-27]","duration":"18.957ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-27 09:36:29.930]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7be1e79a9d8853ce3a26","sql":"SELECT COALESCE(SUM(total_due_amount - paid_amount - total_waive_amount), 0) as overdue_amount FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-27 3 9 2]","duration":"18.3678ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-27 09:36:29.952]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7be1e79a9d8853ce3a26","sql":"\n\t\tSELECT\n\t\t\tDATE(due_date) as date,\n\t\t\tCOALESCE(SUM(total_due_amount), 0) as due_amount\n\t\tFROM business_repayment_bills\n\t\tWHERE due_date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)\n\t\t\tAND due_date < CURDATE()\n\t\tGROUP BY DATE(due_date)\n\t\tORDER BY date\n\t, [7]","duration":"68.6029ms","duration_ms":68}
{"level":"dev.info","ts":"[2025-08-27 09:36:29.953]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7be1e79a9d8853ce3a26","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-27 3 9 2]","duration":"70.7202ms","duration_ms":70}
{"level":"dev.info","ts":"[2025-08-27 09:36:29.953]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7be1e79a9d8853ce3a26","sql":"SELECT COALESCE(SUM(total_due_amount - total_waive_amount), 0) as due_amount,COALESCE(SUM(due_principal), 0) as due_principal_amount FROM `business_repayment_bills` WHERE DATE(due_date) <= ? LIMIT 1, [2025-08-27]","duration":"70.2021ms","duration_ms":70}
{"level":"dev.info","ts":"[2025-08-27 09:36:29.953]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7be1e79a9d8853ce3a26","sql":"\n\t\tSELECT\n\t\t\tDATE(completed_at) as date,\n\t\t\tCOALESCE(SUM(amount), 0) as disbursement_amount\n\t\tFROM business_payment_transactions\n\t\tWHERE status = ?\n\t\t\tAND type = ?\n\t\t\tAND DATE(completed_at) >= DATE_SUB(CURDATE(), INTERVAL ? DAY)\n\t\t\tAND DATE(completed_at) < CURDATE()\n\t\tGROUP BY DATE(completed_at)\n\t\tORDER BY date\n\t, [2 DISBURSEMENT 7]","duration":"70.2021ms","duration_ms":70}
{"level":"dev.info","ts":"[2025-08-27 09:36:29.953]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7be1e79a9d8853ce3a26","sql":"\n\t\tSELECT\n\t\t\tDATE(FROM_UNIXTIME(createtime)) as date,\n\t\t\tCOUNT(*) as registration_count\n\t\tFROM business_app_account\n\t\tWHERE createtime >= UNIX_TIMESTAMP(DATE_SUB(CURDATE(), INTERVAL ? DAY))\n\t\t\tAND createtime < UNIX_TIMESTAMP(CURDATE())\n\t\tGROUP BY DATE(FROM_UNIXTIME(createtime))\n\t\tORDER BY date\n\t, [7]","duration":"70.1978ms","duration_ms":70}
{"level":"dev.info","ts":"[2025-08-27 09:36:29.954]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7be1e79a9d8853ce3a26","sql":"\n\t\tSELECT\n\t\t\tDATE(due_date) as date,\n\t\t\tCOALESCE(SUM(paid_amount), 0) as repayment_amount\n\t\tFROM business_repayment_bills\n\t\tWHERE paid_amount > 0\n\t\t\tAND due_date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)\n\t\t\tAND due_date < CURDATE()\n\t\tGROUP BY DATE(due_date)\n\t\tORDER BY date\n\t, [7]","duration":"70.7348ms","duration_ms":70}
{"level":"dev.info","ts":"[2025-08-27 09:36:29.972]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7be1e79a9d8853ce3a26","sql":"SELECT * FROM `business_payment_transactions` WHERE `status` = ? and `type` = ?, [2 DISBURSEMENT]","duration":"89.4113ms","duration_ms":89}
{"level":"dev.info","ts":"[2025-08-27 09:36:29.972]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7be1e79a9d8853ce3a26","sql":"SELECT COALESCE(SUM(total_due_amount - total_waive_amount), 0) as due_amount,COALESCE(SUM(due_principal), 0) as due_principal_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [2025-08-27 2025-08-27]","duration":"90.4431ms","duration_ms":90}
{"level":"dev.info","ts":"[2025-08-27 09:36:29.972]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7be1e79a9d8853ce3a26","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_payment_transactions` WHERE `status` = ? and `type` = ? and DATE(completed_at) BETWEEN ? and ? LIMIT 1, [2 DISBURSEMENT 2025-08-27 2025-08-27]","duration":"42.0332ms","duration_ms":42}
{"level":"dev.info","ts":"[2025-08-27 09:36:29.980]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7be1e782ad04f87d0885","sql":"SELECT COALESCE(SUM(paid_amount), 0) as repayment_amount FROM `business_repayment_bills` WHERE DATE(due_date) <= ? LIMIT 1, [2025-08-27]","duration":"26.788ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-08-27 09:36:29.980]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7be1e782ad04f87d0885","sql":"SELECT COALESCE(SUM(total_due_amount - paid_amount - total_waive_amount), 0) as overdue_amount FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-27 3 9 2]","duration":"27.3207ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-08-27 09:36:30.005]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7be1e782ad04f87d0885","sql":"SELECT COALESCE(SUM(amount), 0) as total_amount, COUNT(*) as order_count FROM `business_payment_transactions` WHERE `status` = ? and `type` = ? LIMIT 1, [2 DISBURSEMENT]","duration":"32.6187ms","duration_ms":32}
{"level":"dev.info","ts":"[2025-08-27 09:36:30.028]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7be1e782ad04f87d0885","sql":"SELECT COALESCE(SUM(paid_amount), 0) as repayment_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [2025-08-27 2025-08-27]","duration":"56.1524ms","duration_ms":56}
{"level":"dev.info","ts":"[2025-08-27 09:36:30.028]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7be1e782ad04f87d0885","sql":"SELECT COALESCE(SUM(principal), 0) as principal_amount FROM `business_loan_orders` WHERE DATE(disbursed_at) BETWEEN ? and ? LIMIT 1, [2025-08-27 2025-08-27]","duration":"55.627ms","duration_ms":55}
{"level":"dev.info","ts":"[2025-08-27 09:36:30.048]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7be1e782ad04f87d0885","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_payment_transactions` WHERE `status` = ? and `type` = ? LIMIT 1, [2 DISBURSEMENT]","duration":"43.3559ms","duration_ms":43}
{"level":"dev.info","ts":"[2025-08-27 09:36:30.070]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7be1e79a9d880f050a42","sql":"SELECT COALESCE(SUM(principal), 0) as principal_amount FROM `business_loan_orders` WHERE `id` IN (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) LIMIT 1, [60 61 62 63 64 65 66 67 68 69 70 73 75 76 77 78 79 80 81 82 83 84 85 86 87 89 88]","duration":"21.8286ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-27 09:37:13.703]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT id,accountID,businessID,password,salt,name,username FROM `business_account` WHERE (`username` = ? or `email` = ?) and `mobile` = ? LIMIT 1, [fincore fincore ***********]","duration":"151.504ms","duration_ms":151}
{"level":"dev.info","ts":"[2025-08-27 09:37:13.785]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"INSERT INTO `admin_operate_log` (`result`,`ip`,`region`,`uid`,`operate_name`,`created_at`,`detail`) VALUES (?,?,?,?,?,?,?), [失败  未知 1 登录 2025-08-27 09:37:13.7356141 +0800 CST m=+16.********* 失败：短信验证码错误！]","duration":"50.161ms","duration_ms":50}
{"level":"dev.info","ts":"[2025-08-27 09:37:22.400]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT id,accountID,businessID,password,salt,name,username FROM `business_account` WHERE (`username` = ? or `email` = ?) and `mobile` = ? LIMIT 1, [fincore fincore ***********]","duration":"54.1532ms","duration_ms":54}
{"level":"dev.info","ts":"[2025-08-27 09:37:22.442]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"INSERT INTO `admin_operate_log` (`uid`,`operate_name`,`created_at`,`detail`,`result`,`ip`,`region`) VALUES (?,?,?,?,?,?,?), [1 登录 2025-08-27 09:37:22.4106594 +0800 CST m=+24.********* 失败：短信验证码错误！ 失败  未知]","duration":"30.3136ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-08-27 09:37:30.062]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT id,accountID,businessID,password,salt,name,username FROM `business_account` WHERE (`username` = ? or `email` = ?) and `mobile` = ? LIMIT 1, [fincore fincore ***********]","duration":"80.3931ms","duration_ms":80}
{"level":"dev.info","ts":"[2025-08-27 09:37:30.186]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `business_account` SET `lastLoginIp` = ?,`loginstatus` = ?,`lastLoginTime` = ? WHERE `id` = ?, [ 1 ********** 1]","duration":"95.7418ms","duration_ms":95}
{"level":"dev.info","ts":"[2025-08-27 09:37:30.247]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_role_access` WHERE `uid` = ?, [1]","duration":"60.534ms","duration_ms":60}
{"level":"dev.info","ts":"[2025-08-27 09:37:30.290]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_role` WHERE `id` IN (?), [1]","duration":"42.8095ms","duration_ms":42}
{"level":"dev.info","ts":"[2025-08-27 09:37:30.315]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"INSERT INTO `admin_operate_log` (`uid`,`operate_name`,`created_at`,`result`,`detail`,`ip`,`region`) VALUES (?,?,?,?,?,?,?), [1 登录 2025-08-27 09:37:30.2901441 +0800 CST m=+32.********* 成功 成功  未知]","duration":"24.8481ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-08-27 09:37:30.380]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT id,businessID,username,name,nickname,city,company,avatar,status FROM `business_account` WHERE `id` = ? LIMIT 1, [1]","duration":"47.5799ms","duration_ms":47}
{"level":"dev.info","ts":"[2025-08-27 09:37:30.414]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `common_config` WHERE `keyname` = ? LIMIT 1, [rooturl]","duration":"33.4327ms","duration_ms":33}
{"level":"dev.info","ts":"[2025-08-27 09:37:30.442]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_role_access` WHERE `uid` = ?, [1]","duration":"109.14ms","duration_ms":109}
{"level":"dev.info","ts":"[2025-08-27 09:37:30.470]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_role` WHERE `id` IN (?), [1]","duration":"27.8051ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-08-27 09:37:30.527]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `status` = ? and `type` IN (?,?) ORDER BY orderNo asc, [0 0 1]","duration":"57.7555ms","duration_ms":57}
{"level":"dev.info","ts":"[2025-08-27 09:37:30.560]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 8]","duration":"31.3179ms","duration_ms":31}
{"level":"dev.info","ts":"[2025-08-27 09:37:30.579]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 70]","duration":"17.8794ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-27 09:37:30.637]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 69]","duration":"58.3568ms","duration_ms":58}
{"level":"dev.info","ts":"[2025-08-27 09:37:30.673]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 68]","duration":"35.3204ms","duration_ms":35}
{"level":"dev.info","ts":"[2025-08-27 09:37:30.707]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 463]","duration":"34.9031ms","duration_ms":34}
{"level":"dev.info","ts":"[2025-08-27 09:37:30.724]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 460]","duration":"15.0147ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-27 09:37:30.751]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 451]","duration":"27.1807ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-08-27 09:37:30.807]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 458]","duration":"55.8177ms","duration_ms":55}
{"level":"dev.info","ts":"[2025-08-27 09:37:30.825]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 450]","duration":"18.2415ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-27 09:37:30.847]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 457]","duration":"21.8714ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-27 09:37:30.915]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 456]","duration":"67.7242ms","duration_ms":67}
{"level":"dev.info","ts":"[2025-08-27 09:37:30.962]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 464]","duration":"47.1174ms","duration_ms":47}
{"level":"dev.info","ts":"[2025-08-27 09:37:31.000]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 465]","duration":"38.3737ms","duration_ms":38}
{"level":"dev.info","ts":"[2025-08-27 09:37:31.042]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 455]","duration":"39.6112ms","duration_ms":39}
{"level":"dev.info","ts":"[2025-08-27 09:37:31.080]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 440]","duration":"38.0885ms","duration_ms":38}
{"level":"dev.info","ts":"[2025-08-27 09:37:31.101]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 439]","duration":"20.9093ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-27 09:37:31.132]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 467]","duration":"30.3636ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-08-27 09:37:31.156]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 468]","duration":"23.4481ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-27 09:37:31.230]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 469]","duration":"73.9424ms","duration_ms":73}
{"level":"dev.info","ts":"[2025-08-27 09:37:31.297]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 466]","duration":"67.7828ms","duration_ms":67}
{"level":"dev.info","ts":"[2025-08-27 09:37:31.346]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 453]","duration":"48.7354ms","duration_ms":48}
{"level":"dev.info","ts":"[2025-08-27 09:37:31.364]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 459]","duration":"17.9267ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-27 09:37:31.391]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 452]","duration":"26.7905ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-08-27 09:37:31.468]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 438]","duration":"77.1548ms","duration_ms":77}
{"level":"dev.info","ts":"[2025-08-27 09:37:31.504]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 437]","duration":"35.903ms","duration_ms":35}
{"level":"dev.info","ts":"[2025-08-27 09:37:31.536]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 435]","duration":"31.9763ms","duration_ms":31}
{"level":"dev.info","ts":"[2025-08-27 09:37:31.577]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 436]","duration":"41.0937ms","duration_ms":41}
{"level":"dev.info","ts":"[2025-08-27 09:37:31.597]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 383]","duration":"20.4608ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-27 09:37:31.618]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 75]","duration":"19.992ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-27 09:37:31.662]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 97]","duration":"43.7977ms","duration_ms":43}
{"level":"dev.info","ts":"[2025-08-27 09:37:31.681]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 374]","duration":"19.6801ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-27 09:37:31.711]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 74]","duration":"29.3909ms","duration_ms":29}
{"level":"dev.info","ts":"[2025-08-27 09:37:31.735]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 11]","duration":"23.6702ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-27 09:37:31.754]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 12]","duration":"19.34ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-27 09:37:31.783]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 63]","duration":"28.8999ms","duration_ms":28}
{"level":"dev.info","ts":"[2025-08-27 09:37:31.809]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 13]","duration":"25.9963ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-08-27 09:37:31.826]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 61]","duration":"17.1442ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-27 09:37:32.045]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bf05dd26bbcd5b5d550","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-27 3 9 2]","duration":"53.35ms","duration_ms":53}
{"level":"dev.info","ts":"[2025-08-27 09:37:32.047]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bf05dd26bbcd5b5d550","sql":"SELECT * FROM `business_payment_transactions` WHERE `status` = ? and `type` = ? and DATE(completed_at) BETWEEN ? and ?, [2 DISBURSEMENT 2025-08-27 2025-08-27]","duration":"55.3923ms","duration_ms":55}
{"level":"dev.info","ts":"[2025-08-27 09:37:32.081]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bf05dd26bbcd5b5d550","sql":"SELECT COALESCE(SUM(total_due_amount - paid_amount - total_waive_amount), 0) as overdue_amount FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-27 3 9 2]","duration":"34.4728ms","duration_ms":34}
{"level":"dev.info","ts":"[2025-08-27 09:37:32.081]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bf05dd26bbcd5b5d550","sql":"SELECT COALESCE(SUM(amount), 0) as total_amount, COUNT(*) as order_count FROM `business_payment_transactions` WHERE `status` = ? and `type` = ? and DATE(completed_at) BETWEEN ? and ? LIMIT 1, [2 DISBURSEMENT 2025-08-27 2025-08-27]","duration":"33.9376ms","duration_ms":33}
{"level":"dev.info","ts":"[2025-08-27 09:37:32.131]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bf05dd26bbcd5b5d550","sql":"SELECT COALESCE(SUM(total_due_amount - total_waive_amount), 0) as due_amount,COALESCE(SUM(due_principal), 0) as due_principal_amount FROM `business_repayment_bills` WHERE DATE(due_date) <= ? LIMIT 1, [2025-08-27]","duration":"135.9856ms","duration_ms":135}
{"level":"dev.info","ts":"[2025-08-27 09:37:32.131]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bf05dd26bbcd5b5d550","sql":"\n\t\tSELECT\n\t\t\tDATE(completed_at) as date,\n\t\t\tCOALESCE(SUM(amount), 0) as disbursement_amount\n\t\tFROM business_payment_transactions\n\t\tWHERE status = ?\n\t\t\tAND type = ?\n\t\t\tAND DATE(completed_at) >= DATE_SUB(CURDATE(), INTERVAL ? DAY)\n\t\t\tAND DATE(completed_at) < CURDATE()\n\t\tGROUP BY DATE(completed_at)\n\t\tORDER BY date\n\t, [2 DISBURSEMENT 7]","duration":"135.4499ms","duration_ms":135}
{"level":"dev.info","ts":"[2025-08-27 09:37:32.131]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bf05dd26bbcd5b5d550","sql":"\n\t\tSELECT\n\t\t\tDATE(due_date) as date,\n\t\t\tCOALESCE(SUM(paid_amount), 0) as repayment_amount\n\t\tFROM business_repayment_bills\n\t\tWHERE paid_amount > 0\n\t\t\tAND due_date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)\n\t\t\tAND due_date < CURDATE()\n\t\tGROUP BY DATE(due_date)\n\t\tORDER BY date\n\t, [7]","duration":"135.9856ms","duration_ms":135}
{"level":"dev.info","ts":"[2025-08-27 09:37:32.131]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bf05dd26bbcd5b5d550","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_payment_transactions` WHERE `status` = ? and `type` = ? and DATE(completed_at) BETWEEN ? and ? LIMIT 1, [2 DISBURSEMENT 2025-08-27 2025-08-27]","duration":"49.6913ms","duration_ms":49}
{"level":"dev.info","ts":"[2025-08-27 09:37:32.131]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bf05d8a2adcf52c262c","sql":"SELECT COALESCE(SUM(total_due_amount - total_waive_amount), 0) as due_amount,COALESCE(SUM(due_principal), 0) as due_principal_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [2025-08-27 2025-08-27]","duration":"139.5625ms","duration_ms":139}
{"level":"dev.info","ts":"[2025-08-27 09:37:32.132]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bf05d8a2adcf52c262c","sql":"\n\t\tSELECT\n\t\t\tDATE(FROM_UNIXTIME(createtime)) as date,\n\t\t\tCOUNT(*) as registration_count\n\t\tFROM business_app_account\n\t\tWHERE createtime >= UNIX_TIMESTAMP(DATE_SUB(CURDATE(), INTERVAL ? DAY))\n\t\t\tAND createtime < UNIX_TIMESTAMP(CURDATE())\n\t\tGROUP BY DATE(FROM_UNIXTIME(createtime))\n\t\tORDER BY date\n\t, [7]","duration":"137.5484ms","duration_ms":137}
{"level":"dev.info","ts":"[2025-08-27 09:37:32.132]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bf05d8a2adcf52c262c","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-27 3 9 2]","duration":"137.5704ms","duration_ms":137}
{"level":"dev.info","ts":"[2025-08-27 09:37:32.147]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bf05d8a2adcf52c262c","sql":"\n\t\tSELECT\n\t\t\tDATE(due_date) as date,\n\t\t\tCOALESCE(SUM(total_due_amount), 0) as due_amount\n\t\tFROM business_repayment_bills\n\t\tWHERE due_date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)\n\t\t\tAND due_date < CURDATE()\n\t\tGROUP BY DATE(due_date)\n\t\tORDER BY date\n\t, [7]","duration":"151.5362ms","duration_ms":151}
{"level":"dev.info","ts":"[2025-08-27 09:37:32.163]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bf05d8a2adcf52c262c","sql":"SELECT COALESCE(SUM(paid_amount), 0) as repayment_amount FROM `business_repayment_bills` WHERE DATE(due_date) <= ? LIMIT 1, [2025-08-27]","duration":"32.2165ms","duration_ms":32}
{"level":"dev.info","ts":"[2025-08-27 09:37:32.163]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bf05d8a2adcf52c262c","sql":"SELECT * FROM `business_payment_transactions` WHERE `status` = ? and `type` = ?, [2 DISBURSEMENT]","duration":"168.7259ms","duration_ms":168}
{"level":"dev.info","ts":"[2025-08-27 09:37:32.183]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bf05d8a2adcf52c262c","sql":"SELECT COALESCE(SUM(principal), 0) as principal_amount FROM `business_loan_orders` WHERE DATE(disbursed_at) BETWEEN ? and ? LIMIT 1, [2025-08-27 2025-08-27]","duration":"51.8626ms","duration_ms":51}
{"level":"dev.info","ts":"[2025-08-27 09:37:32.183]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bf05d8a2adcf52c262c","sql":"SELECT COALESCE(SUM(paid_amount), 0) as repayment_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [2025-08-27 2025-08-27]","duration":"51.3307ms","duration_ms":51}
{"level":"dev.info","ts":"[2025-08-27 09:37:32.194]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bf05d8a2adcf52c262c","sql":"SELECT COALESCE(SUM(amount), 0) as total_amount, COUNT(*) as order_count FROM `business_payment_transactions` WHERE `status` = ? and `type` = ? LIMIT 1, [2 DISBURSEMENT]","duration":"31.0269ms","duration_ms":31}
{"level":"dev.info","ts":"[2025-08-27 09:37:32.194]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bf05d8a2adcf52c262c","sql":"SELECT COALESCE(SUM(total_due_amount - paid_amount - total_waive_amount), 0) as overdue_amount FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-27 3 9 2]","duration":"61.6586ms","duration_ms":61}
{"level":"dev.info","ts":"[2025-08-27 09:37:32.220]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bf05d8a2adcf52c262c","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_payment_transactions` WHERE `status` = ? and `type` = ? LIMIT 1, [2 DISBURSEMENT]","duration":"25.5834ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-08-27 09:37:32.283]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f7bf05dca62b43d25d9ce","sql":"SELECT COALESCE(SUM(principal), 0) as principal_amount FROM `business_loan_orders` WHERE `id` IN (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) LIMIT 1, [60 61 62 63 64 65 66 67 68 69 70 73 75 76 77 78 79 80 81 82 83 84 85 86 87 89 88]","duration":"63.0244ms","duration_ms":63}
{"level":"dev.info","ts":"[2025-08-27 10:55:49.216]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT id,accountID,businessID,password,salt,name,username FROM `business_account` WHERE (`username` = ? or `email` = ?) and `mobile` = ? LIMIT 1, [fincore fincore ***********]","duration":"28.1067ms","duration_ms":28}
{"level":"dev.info","ts":"[2025-08-27 10:55:49.294]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `business_account` SET `loginstatus` = ?,`lastLoginTime` = ?,`lastLoginIp` = ? WHERE `id` = ?, [1 **********  1]","duration":"54.2275ms","duration_ms":54}
{"level":"dev.info","ts":"[2025-08-27 10:55:49.321]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_role_access` WHERE `uid` = ?, [1]","duration":"26.9081ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-08-27 10:55:49.366]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_role` WHERE `id` IN (?), [1]","duration":"44.0246ms","duration_ms":44}
{"level":"dev.info","ts":"[2025-08-27 10:55:49.411]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"INSERT INTO `admin_operate_log` (`detail`,`ip`,`region`,`uid`,`operate_name`,`created_at`,`result`) VALUES (?,?,?,?,?,?,?), [成功  未知 1 登录 2025-08-27 10:55:49.3671401 +0800 CST m=+48.********* 成功]","duration":"43.7835ms","duration_ms":43}
{"level":"dev.info","ts":"[2025-08-27 10:55:49.515]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_role_access` WHERE `uid` = ?, [1]","duration":"79.412ms","duration_ms":79}
{"level":"dev.info","ts":"[2025-08-27 10:55:49.588]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_role` WHERE `id` IN (?), [1]","duration":"73.0696ms","duration_ms":73}
{"level":"dev.info","ts":"[2025-08-27 10:55:49.699]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT id,businessID,username,name,nickname,city,company,avatar,status FROM `business_account` WHERE `id` = ? LIMIT 1, [1]","duration":"264.832ms","duration_ms":264}
{"level":"dev.info","ts":"[2025-08-27 10:55:49.709]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `status` = ? and `type` IN (?,?) ORDER BY orderNo asc, [0 0 1]","duration":"120.4654ms","duration_ms":120}
{"level":"dev.info","ts":"[2025-08-27 10:55:49.753]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `common_config` WHERE `keyname` = ? LIMIT 1, [rooturl]","duration":"45.336ms","duration_ms":45}
{"level":"dev.info","ts":"[2025-08-27 10:55:49.830]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 8]","duration":"111.5264ms","duration_ms":111}
{"level":"dev.info","ts":"[2025-08-27 10:55:49.913]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 70]","duration":"82.6526ms","duration_ms":82}
{"level":"dev.info","ts":"[2025-08-27 10:55:50.003]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 69]","duration":"90.0247ms","duration_ms":90}
{"level":"dev.info","ts":"[2025-08-27 10:55:50.032]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 68]","duration":"29.1963ms","duration_ms":29}
{"level":"dev.info","ts":"[2025-08-27 10:55:50.088]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 463]","duration":"55.1074ms","duration_ms":55}
{"level":"dev.info","ts":"[2025-08-27 10:55:50.118]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 460]","duration":"30.1502ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-08-27 10:55:50.171]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 451]","duration":"53.5931ms","duration_ms":53}
{"level":"dev.info","ts":"[2025-08-27 10:55:50.195]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 458]","duration":"23.4883ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-27 10:55:50.229]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 450]","duration":"34.173ms","duration_ms":34}
{"level":"dev.info","ts":"[2025-08-27 10:55:50.254]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 457]","duration":"24.5258ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-08-27 10:55:50.287]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 456]","duration":"32.9901ms","duration_ms":32}
{"level":"dev.info","ts":"[2025-08-27 10:55:50.341]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 464]","duration":"53.6834ms","duration_ms":53}
{"level":"dev.info","ts":"[2025-08-27 10:55:50.436]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 465]","duration":"95.7719ms","duration_ms":95}
{"level":"dev.info","ts":"[2025-08-27 10:55:50.476]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 455]","duration":"39.5868ms","duration_ms":39}
{"level":"dev.info","ts":"[2025-08-27 10:55:50.614]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 440]","duration":"137.3676ms","duration_ms":137}
{"level":"dev.info","ts":"[2025-08-27 10:55:50.648]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 439]","duration":"33.2394ms","duration_ms":33}
{"level":"dev.info","ts":"[2025-08-27 10:55:50.683]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 467]","duration":"35.3126ms","duration_ms":35}
{"level":"dev.info","ts":"[2025-08-27 10:55:50.749]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 468]","duration":"65.3089ms","duration_ms":65}
{"level":"dev.info","ts":"[2025-08-27 10:55:50.819]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 469]","duration":"70.1366ms","duration_ms":70}
{"level":"dev.info","ts":"[2025-08-27 10:55:50.838]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 466]","duration":"19.0639ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-27 10:55:50.857]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 453]","duration":"18.4105ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-27 10:55:50.876]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 459]","duration":"19.4573ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-27 10:55:50.898]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 452]","duration":"21.6096ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-27 10:55:50.963]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 438]","duration":"65.3679ms","duration_ms":65}
{"level":"dev.info","ts":"[2025-08-27 10:55:51.006]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 437]","duration":"42.694ms","duration_ms":42}
{"level":"dev.info","ts":"[2025-08-27 10:55:51.062]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 435]","duration":"56.5247ms","duration_ms":56}
{"level":"dev.info","ts":"[2025-08-27 10:55:51.109]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 436]","duration":"46.3424ms","duration_ms":46}
{"level":"dev.info","ts":"[2025-08-27 10:55:51.128]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 383]","duration":"18.29ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-27 10:55:51.149]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 75]","duration":"21.9282ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-27 10:55:51.191]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 97]","duration":"41.3986ms","duration_ms":41}
{"level":"dev.info","ts":"[2025-08-27 10:55:51.213]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 374]","duration":"21.3778ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-27 10:55:51.270]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 74]","duration":"56.7629ms","duration_ms":56}
{"level":"dev.info","ts":"[2025-08-27 10:55:51.304]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 11]","duration":"34.3587ms","duration_ms":34}
{"level":"dev.info","ts":"[2025-08-27 10:55:51.423]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 12]","duration":"118.7894ms","duration_ms":118}
{"level":"dev.info","ts":"[2025-08-27 10:55:51.461]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 63]","duration":"37.1196ms","duration_ms":37}
{"level":"dev.info","ts":"[2025-08-27 10:55:51.505]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 13]","duration":"44.2143ms","duration_ms":44}
{"level":"dev.info","ts":"[2025-08-27 10:55:51.580]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 61]","duration":"75.0643ms","duration_ms":75}
{"level":"dev.info","ts":"[2025-08-27 10:55:52.369]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f8036bde0d29c2f61a03c","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-27 3 9 2]","duration":"67.7873ms","duration_ms":67}
{"level":"dev.info","ts":"[2025-08-27 10:55:52.369]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f8036bde0d29c2f61a03c","sql":"\n\t\tSELECT\n\t\t\tDATE(FROM_UNIXTIME(createtime)) as date,\n\t\t\tCOUNT(*) as registration_count\n\t\tFROM business_app_account\n\t\tWHERE createtime >= UNIX_TIMESTAMP(DATE_SUB(CURDATE(), INTERVAL ? DAY))\n\t\t\tAND createtime < UNIX_TIMESTAMP(CURDATE())\n\t\tGROUP BY DATE(FROM_UNIXTIME(createtime))\n\t\tORDER BY date\n\t, [7]","duration":"67.7873ms","duration_ms":67}
{"level":"dev.info","ts":"[2025-08-27 10:55:52.413]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f8036bde0d29c2f61a03c","sql":"SELECT COALESCE(SUM(total_due_amount - paid_amount - total_waive_amount), 0) as overdue_amount FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-27 3 9 2]","duration":"41.843ms","duration_ms":41}
{"level":"dev.info","ts":"[2025-08-27 10:55:52.443]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f8036bde0d29c2f61a03c","sql":"SELECT * FROM `business_payment_transactions` WHERE `status` = ? and `type` = ? and DATE(completed_at) BETWEEN ? and ?, [2 DISBURSEMENT 2025-08-27 2025-08-27]","duration":"140.9795ms","duration_ms":140}
{"level":"dev.info","ts":"[2025-08-27 10:55:52.443]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f8036bde0d29c2f61a03c","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-27 3 9 2]","duration":"140.9795ms","duration_ms":140}
{"level":"dev.info","ts":"[2025-08-27 10:55:52.443]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f8036bde0d29c2f61a03c","sql":"SELECT COALESCE(SUM(total_due_amount - total_waive_amount), 0) as due_amount,COALESCE(SUM(due_principal), 0) as due_principal_amount FROM `business_repayment_bills` WHERE DATE(due_date) <= ? LIMIT 1, [2025-08-27]","duration":"140.4338ms","duration_ms":140}
{"level":"dev.info","ts":"[2025-08-27 10:55:52.443]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f8036bde0d29c2f61a03c","sql":"SELECT COALESCE(SUM(total_due_amount - total_waive_amount), 0) as due_amount,COALESCE(SUM(due_principal), 0) as due_principal_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [2025-08-27 2025-08-27]","duration":"140.9659ms","duration_ms":140}
{"level":"dev.info","ts":"[2025-08-27 10:55:52.458]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f8036bde0d29c2f61a03c","sql":"\n\t\tSELECT\n\t\t\tDATE(completed_at) as date,\n\t\t\tCOALESCE(SUM(amount), 0) as disbursement_amount\n\t\tFROM business_payment_transactions\n\t\tWHERE status = ?\n\t\t\tAND type = ?\n\t\t\tAND DATE(completed_at) >= DATE_SUB(CURDATE(), INTERVAL ? DAY)\n\t\t\tAND DATE(completed_at) < CURDATE()\n\t\tGROUP BY DATE(completed_at)\n\t\tORDER BY date\n\t, [2 DISBURSEMENT 7]","duration":"155.4259ms","duration_ms":155}
{"level":"dev.info","ts":"[2025-08-27 10:55:52.503]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f8036bde0d29c2f61a03c","sql":"\n\t\tSELECT\n\t\t\tDATE(due_date) as date,\n\t\t\tCOALESCE(SUM(total_due_amount), 0) as due_amount\n\t\tFROM business_repayment_bills\n\t\tWHERE due_date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)\n\t\t\tAND due_date < CURDATE()\n\t\tGROUP BY DATE(due_date)\n\t\tORDER BY date\n\t, [7]","duration":"200.0372ms","duration_ms":200}
{"level":"dev.info","ts":"[2025-08-27 10:55:52.503]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f8036bde0d29c2f61a03c","sql":"\n\t\tSELECT\n\t\t\tDATE(due_date) as date,\n\t\t\tCOALESCE(SUM(paid_amount), 0) as repayment_amount\n\t\tFROM business_repayment_bills\n\t\tWHERE paid_amount > 0\n\t\t\tAND due_date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)\n\t\t\tAND due_date < CURDATE()\n\t\tGROUP BY DATE(due_date)\n\t\tORDER BY date\n\t, [7]","duration":"200.0372ms","duration_ms":200}
{"level":"dev.info","ts":"[2025-08-27 10:55:52.503]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f8036bde0d29c2f61a03c","sql":"SELECT * FROM `business_payment_transactions` WHERE `status` = ? and `type` = ?, [2 DISBURSEMENT]","duration":"200.5693ms","duration_ms":200}
{"level":"dev.info","ts":"[2025-08-27 10:55:52.523]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f8036bde0d29c2f61a03c","sql":"SELECT COALESCE(SUM(paid_amount), 0) as repayment_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [2025-08-27 2025-08-27]","duration":"79.6687ms","duration_ms":79}
{"level":"dev.info","ts":"[2025-08-27 10:55:52.523]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f8036bde0d29c2f61a03c","sql":"SELECT COALESCE(SUM(amount), 0) as total_amount, COUNT(*) as order_count FROM `business_payment_transactions` WHERE `status` = ? and `type` = ? and DATE(completed_at) BETWEEN ? and ? LIMIT 1, [2 DISBURSEMENT 2025-08-27 2025-08-27]","duration":"80.1773ms","duration_ms":80}
{"level":"dev.info","ts":"[2025-08-27 10:55:52.523]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f8036bde0d29c2f61a03c","sql":"SELECT COALESCE(SUM(total_due_amount - paid_amount - total_waive_amount), 0) as overdue_amount FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-27 3 9 2]","duration":"79.6687ms","duration_ms":79}
{"level":"dev.info","ts":"[2025-08-27 10:55:52.550]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f8036bde0d29c2f61a03c","sql":"SELECT COALESCE(SUM(amount), 0) as total_amount, COUNT(*) as order_count FROM `business_payment_transactions` WHERE `status` = ? and `type` = ? LIMIT 1, [2 DISBURSEMENT]","duration":"47.2153ms","duration_ms":47}
{"level":"dev.info","ts":"[2025-08-27 10:55:52.550]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f8036bde0d29c2f61a03c","sql":"SELECT COALESCE(SUM(paid_amount), 0) as repayment_amount FROM `business_repayment_bills` WHERE DATE(due_date) <= ? LIMIT 1, [2025-08-27]","duration":"47.2153ms","duration_ms":47}
{"level":"dev.info","ts":"[2025-08-27 10:55:52.559]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f8036bde0d29c2f61a03c","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_payment_transactions` WHERE `status` = ? and `type` = ? and DATE(completed_at) BETWEEN ? and ? LIMIT 1, [2 DISBURSEMENT 2025-08-27 2025-08-27]","duration":"36.4394ms","duration_ms":36}
{"level":"dev.info","ts":"[2025-08-27 10:55:52.597]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f8036bde0d29ce4297e9c","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_payment_transactions` WHERE `status` = ? and `type` = ? LIMIT 1, [2 DISBURSEMENT]","duration":"47.4111ms","duration_ms":47}
{"level":"dev.info","ts":"[2025-08-27 10:55:52.614]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f8036bde0d29c89cfdd16","sql":"SELECT COALESCE(SUM(principal), 0) as principal_amount FROM `business_loan_orders` WHERE DATE(disbursed_at) BETWEEN ? and ? LIMIT 1, [2025-08-27 2025-08-27]","duration":"54.8507ms","duration_ms":54}
{"level":"dev.info","ts":"[2025-08-27 10:55:52.673]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f8036bde0d29c89cfdd16","sql":"SELECT COALESCE(SUM(principal), 0) as principal_amount FROM `business_loan_orders` WHERE `id` IN (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) LIMIT 1, [60 61 62 63 64 65 66 67 68 69 70 73 75 76 77 78 79 80 81 82 83 84 85 86 87 89 88]","duration":"75.2967ms","duration_ms":75}
{"level":"dev.info","ts":"[2025-08-27 10:56:00.043]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.status as transaction_status\n\t\tFROM business_loan_orders blo\n\t\tINNER JOIN business_payment_transactions bpt ON blo.order_no = bpt.order_no\n\t\tWHERE blo.status = ?\n\t\t  AND bpt.type = ?\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.channel_transaction_no != ''\n\t\t  AND bpt.channel_transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0 DISBURSEMENT 1]","duration":"40.331ms","duration_ms":40}
{"level":"dev.info","ts":"[2025-08-27 10:56:00.073]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"70.6545ms","duration_ms":70}
{"level":"dev.info","ts":"[2025-08-27 10:56:00.074]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"71.159ms","duration_ms":71}
{"level":"dev.info","ts":"[2025-08-27 10:56:00.159]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [RP1756256998710870238]","duration":"76.2944ms","duration_ms":76}
{"level":"dev.info","ts":"[2025-08-27 10:56:00.579]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `business_payment_transactions` SET `status` = ? WHERE `transaction_no` = ?, [1 RP1756256998710870238]","duration":"220.7094ms","duration_ms":220}
{"level":"dev.info","ts":"[2025-08-27 10:56:01.096]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [RP1756256998710870238]","duration":"516.9911ms","duration_ms":516}
{"level":"dev.info","ts":"[2025-08-27 10:56:01.196]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [*********************]","duration":"99.3237ms","duration_ms":99}
{"level":"dev.info","ts":"[2025-08-27 10:56:01.549]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `business_payment_transactions` SET `status` = ? WHERE `transaction_no` = ?, [1 *********************]","duration":"62.4485ms","duration_ms":62}
{"level":"dev.info","ts":"[2025-08-27 10:56:01.595]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [*********************]","duration":"45.4078ms","duration_ms":45}
{"level":"dev.info","ts":"[2025-08-27 10:56:01.817]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE (baa.deletedAt = 0) LIMIT 1, []","duration":"39.3176ms","duration_ms":39}
{"level":"dev.info","ts":"[2025-08-27 10:56:01.817]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT id, channel_name FROM `channel` WHERE `channel_status` = ? ORDER BY channel_name ASC, [1]","duration":"38.7361ms","duration_ms":38}
{"level":"dev.info","ts":"[2025-08-27 10:56:01.874]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT blo.id, blo.order_no, blo.user_id, blo.product_rule_id, blo.loan_amount,\n\t\tblo.principal, blo.total_interest, blo.total_guarantee_fee, blo.total_other_fees,\n\t\tblo.total_repayable_amount, blo.amount_paid, blo.channel_id, blo.customer_origin,\n\t\tblo.initial_order_channel_id, blo.status, blo.is_freeze, baa.complaintStatus as complaint_status,\n\t    blo.review_status, blo.sales_assignee_id, blo.collection_assignee_id,\n\t\tblo.reason_for_closure, blo.created_at, blo.updated_at, blo.disbursed_at,blo.completed_at, blo.contract_id,\n\t\tbaa.name as user_name, baa.mobile as user_mobile, baa.idCard as user_id_card, baa.repeatBuyNum as repeat_buy_num,\n\t\tc1.channel_name as channel_name, bpc.channel_name as payment_channel_name,\n\t\tc2.channel_name as initial_order_channel_name,\n\t\tpr.loan_period as loan_period, pr.total_periods as total_periods,\n\t\t sales_user.name as sales_assignee_name, collection_user.name as collection_assignee_name,\n\t\t(SELECT COUNT(*) FROM business_repayment_bills brb WHERE brb.order_id = blo.id AND brb.status = 1) as paid_periods FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE (baa.deletedAt = 0) ORDER BY blo.id DESC LIMIT 20, []","duration":"57.1222ms","duration_ms":57}
{"level":"dev.info","ts":"[2025-08-27 10:56:01.966]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT re.customer_id, re.risk_score\n\t\tFROM risk_evaluations re\n\t\tINNER JOIN (\n\t\t\tSELECT customer_id, MAX(created_at) as latest_time\n\t\t\tFROM risk_evaluations\n\t\t\tWHERE customer_id IN (7)\n\t\t\tGROUP BY customer_id\n\t\t) latest ON re.customer_id = latest.customer_id AND re.created_at = latest.latest_time\n\t, []","duration":"91.0713ms","duration_ms":91}
{"level":"dev.info","ts":"[2025-08-27 10:56:01.997]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_role_access` WHERE `uid` = ?, [1]","duration":"30.4869ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-08-27 10:56:02.036]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_role` WHERE `id` IN (?), [1]","duration":"38.5514ms","duration_ms":38}
{"level":"dev.info","ts":"[2025-08-27 10:56:03.713]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f80396030d2c00e45df41","sql":"SELECT count(*) as count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE (baa.deletedAt = 0) and blo.order_no like ? LIMIT 1, [%******************%]","duration":"100.94ms","duration_ms":100}
{"level":"dev.info","ts":"[2025-08-27 10:56:03.764]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f80396030d2c00e45df41","sql":"SELECT * FROM `business_loan_orders` WHERE `order_no` = ? LIMIT 1, [******************]","duration":"151.8748ms","duration_ms":151}
{"level":"dev.info","ts":"[2025-08-27 10:56:03.766]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f80396028e76878ab3cd3","sql":"SELECT * FROM `business_loan_orders` WHERE `order_no` = ? LIMIT 1, [******************]","duration":"152.9261ms","duration_ms":152}
{"level":"dev.info","ts":"[2025-08-27 10:56:03.811]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f80396030d2c00e45df41","sql":"SELECT blo.id, blo.order_no, blo.user_id, blo.product_rule_id, blo.loan_amount,\n\t\tblo.principal, blo.total_interest, blo.total_guarantee_fee, blo.total_other_fees,\n\t\tblo.total_repayable_amount, blo.amount_paid, blo.channel_id, blo.customer_origin,\n\t\tblo.initial_order_channel_id, blo.status, blo.is_freeze, baa.complaintStatus as complaint_status,\n\t    blo.review_status, blo.sales_assignee_id, blo.collection_assignee_id,\n\t\tblo.reason_for_closure, blo.created_at, blo.updated_at, blo.disbursed_at,blo.completed_at, blo.contract_id,\n\t\tbaa.name as user_name, baa.mobile as user_mobile, baa.idCard as user_id_card, baa.repeatBuyNum as repeat_buy_num,\n\t\tc1.channel_name as channel_name, bpc.channel_name as payment_channel_name,\n\t\tc2.channel_name as initial_order_channel_name,\n\t\tpr.loan_period as loan_period, pr.total_periods as total_periods,\n\t\t sales_user.name as sales_assignee_name, collection_user.name as collection_assignee_name,\n\t\t(SELECT COUNT(*) FROM business_repayment_bills brb WHERE brb.order_id = blo.id AND brb.status = 1) as paid_periods FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE (baa.deletedAt = 0) and blo.order_no like ? ORDER BY blo.id DESC LIMIT 1, [%******************%]","duration":"97.074ms","duration_ms":97}
{"level":"dev.info","ts":"[2025-08-27 10:56:03.820]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f80396030d2c00e45df41","sql":"SELECT * FROM `product_rules` WHERE `id` = ? LIMIT 1, [19]","duration":"55.3182ms","duration_ms":55}
{"level":"dev.info","ts":"[2025-08-27 10:56:03.821]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f80396030d2c00e45df41","sql":"SELECT id, status, amount, created_at as initiated_at, completed_at, channel_transaction_no,error_message\n\t\t FROM `business_payment_transactions` WHERE `order_id` = ? and `user_id` = ? and (deleted_at IS NULL) and `type` = ? ORDER BY created_at ASC, [89 7 DISBURSEMENT]","duration":"56.3181ms","duration_ms":56}
{"level":"dev.info","ts":"[2025-08-27 10:56:03.821]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f80396030d2c00e45df41","sql":"SELECT brb.id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbrb.total_refund_amount,\n\t\t\tbrb.status,\n\t\t\tbrb.due_date,\n\t\t\tbrb.total_waive_amount,\n\t\t\tbrb.paid_at as payment_time FROM business_repayment_bills brb WHERE brb.order_id = ? and brb.user_id = ? ORDER BY brb.period_number ASC, [89 7]","duration":"56.3181ms","duration_ms":56}
{"level":"dev.info","ts":"[2025-08-27 10:56:03.843]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f80396030d2c00e45df41","sql":"SELECT count(*) as count FROM business_payment_transactions bpt WHERE bpt.order_id = ? and (bpt.deleted_at IS NULL) and bpt.type IN (?,?,?,?,?) LIMIT 1, [89 REPAYMENT REFUND WITHHOLD PARTIAL_OFFLINE_REPAYMENT MANUAL_WITHHOLD]","duration":"76.6962ms","duration_ms":76}
{"level":"dev.info","ts":"[2025-08-27 10:56:03.848]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f80396030d2c00e45df41","sql":"\n\t\tSELECT re.customer_id, re.risk_score\n\t\tFROM risk_evaluations re\n\t\tINNER JOIN (\n\t\t\tSELECT customer_id, MAX(created_at) as latest_time\n\t\t\tFROM risk_evaluations\n\t\t\tWHERE customer_id IN (7)\n\t\t\tGROUP BY customer_id\n\t\t) latest ON re.customer_id = latest.customer_id AND re.created_at = latest.latest_time\n\t, []","duration":"37.2518ms","duration_ms":37}
{"level":"dev.info","ts":"[2025-08-27 10:56:03.857]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f80396030d2c00e45df41","sql":"SELECT bpt.id,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.amount,\n\t\t\tbpt.status,\n\t\t\tbpt.type,\n\t\t\tbpt.withhold_type,\n\t\t\tbpt.created_at as initiated_at,\n\t\t\tbpt.completed_at,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.error_message,\n\t\t\tbpt.offline_payment_voucher,\n\t\t\tbrb.period_number,\n\t\t\tbrb.id as bill_id,\n\t\t\tbpc.channel_name as payment_type,\n\t\t\t(select sum(amount) from business_payment_transactions where related_transaction_no = bpt.transaction_no and type = 'REFUND' and status in (1, 2)) as refund_amount FROM business_payment_transactions bpt LEFT JOIN business_repayment_bills brb ON bpt.bill_id = brb.id  LEFT JOIN business_payment_channels bpc ON bpt.payment_channel_id = bpc.id WHERE bpt.order_id = ? and (bpt.deleted_at IS NULL) and bpt.type IN (?,?,?,?,?) ORDER BY bpt.created_at DESC LIMIT 10, [89 REPAYMENT REFUND WITHHOLD PARTIAL_OFFLINE_REPAYMENT MANUAL_WITHHOLD]","duration":"14.0126ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-27 10:56:03.878]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f80396030d2c00e45df41","sql":"SELECT * FROM `business_auth_role_access` WHERE `uid` = ?, [1]","duration":"30.1773ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-08-27 10:56:03.906]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f80396030d2c00e45df41","sql":"SELECT * FROM `business_auth_role` WHERE `id` IN (?), [1]","duration":"27.3513ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-08-27 10:56:03.993]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f80396030d2c00e45df41","sql":"\n\t\tSELECT\n\t\t\tid, order_no, user_id, product_rule_id, loan_amount, principal,\n\t\t\ttotal_interest, total_guarantee_fee, total_other_fees, total_repayable_amount,\n\t\t\tamount_paid, channel_id, payment_channel_id, customer_origin, status,\n\t\t\tis_freeze, review_status, review_remark,\n\t\t\tcreated_at, disbursed_at, completed_at\n\t\tFROM business_loan_orders\n\t\tWHERE id = ?\n\t, [89]","duration":"74.597ms","duration_ms":74}
{"level":"dev.info","ts":"[2025-08-27 10:56:04.035]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f80397267d3447fe01046","sql":"\n\t\tSELECT id, channel_name, channel_code\n\t\tFROM business_payment_channels\n\t\tWHERE id = ?\n\t, [1]","duration":"42.563ms","duration_ms":42}
{"level":"dev.info","ts":"[2025-08-27 10:56:04.035]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f80397267d3447fe01046","sql":"\n\t\tSELECT riskScore\n\t\tFROM business_app_account\n\t\tWHERE id = ? AND riskScore > 0\n\t, [7]","duration":"42.0559ms","duration_ms":42}
{"level":"dev.info","ts":"[2025-08-27 10:56:04.035]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f80397267d3447fe01046","sql":"\n\t\tSELECT\n\t\t\tr.id,\n\t\t\tr.order_id,\n\t\t\tr.content,\n\t\t\tr.user_id,\n\t\t\tCOALESCE(u.username, '') as user_name,\n\t\t\tr.create_time\n\t\tFROM business_order_remarks r\n\t\tLEFT JOIN business_account u ON r.user_id = u.id\n\t\tWHERE r.order_id = ?\n\t\tORDER BY r.create_time DESC\n\t, [89]","duration":"42.563ms","duration_ms":42}
{"level":"dev.info","ts":"[2025-08-27 10:56:04.035]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f80397267d3447fe01046","sql":"\n\t\tSELECT\n\t\t\tCOUNT(*) as total_orders,\n\t\t\tSUM(CASE WHEN status IN (0, 1) THEN 1 ELSE 0 END) as in_progress_orders,\n\t\t\tSUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) as completed_orders,\n\t\t\tMIN(created_at) as first_order_time\n\t\tFROM business_loan_orders\n\t\tWHERE user_id = ?\n\t, [7]","duration":"42.563ms","duration_ms":42}
{"level":"dev.info","ts":"[2025-08-27 10:56:04.035]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f80397267d3447fe01046","sql":"\n\t\tSELECT\n\t\t\tid, name, mobile, idCard, idCardFrontUrl, idCardBackUrl,facePhotoUrl,\n\t\t\trepeatBuyNum, riskScore, address, province, city,\n\t\t\tlastLoginIp, createtime, status,\n\t\t\tallQuota, reminderQuota,\n\t\t\temergencyContact0Name, emergencyContact0Phone, emergencyContact0Relation,\n\t\t\temergencyContact1Name, emergencyContact1Phone, emergencyContact1Relation\n\t\tFROM business_app_account\n\t\tWHERE id = ?\n\t, [7]","duration":"42.563ms","duration_ms":42}
{"level":"dev.info","ts":"[2025-08-27 10:56:04.257]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f80397267d3447fe01046","sql":"\n\t\tSELECT risk_score\n\t\tFROM risk_evaluations\n\t\tWHERE customer_id = ?\n\t\tORDER BY evaluation_time DESC\n\t\tLIMIT 1\n\t, [7]","duration":"221.8308ms","duration_ms":221}
{"level":"dev.info","ts":"[2025-08-27 10:56:04.294]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f80397267d3447fe01046","sql":"SELECT user_id, SUM(CASE WHEN status IN (0, 1) THEN 1 ELSE 0 END) as borrowing_order_count, COUNT(*) as total_order_count FROM `business_loan_orders` WHERE `user_id` IN (?) GROUP BY user_id, [7]","duration":"27.4004ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-08-27 10:56:04.315]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f80397267d3447fe01046","sql":"SELECT baa.id, baa.name, baa.mobile, baa.idCardFrontUrl, baa.idCardBackUrl, baa.idCard, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.emergencyContact0Name, baa.emergencyContact0Phone, baa.emergencyContact0Relation, baa.emergencyContact1Name, baa.emergencyContact1Phone, baa.emergencyContact1Relation,\n\t\t\tbaa.degree, baa.marry, baa.occupation, baa.yearRevenue,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore, baa.facePhotoUrl, baa.availableProductID,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount FROM business_app_account baa LEFT JOIN channel c ON baa.channelId = c.id  LEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1 WHERE baa.id = ? and baa.deletedAt = ? LIMIT 1, [7 0]","duration":"47.8983ms","duration_ms":47}
{"level":"dev.info","ts":"[2025-08-27 10:56:04.340]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f80397267d3447fe01046","sql":"SELECT * FROM `ocr_identity_records` WHERE `user_id` = ? ORDER BY created_at DESC LIMIT 1, [7]","duration":"24.256ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-08-27 10:56:04.374]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f80397267d3447fe01046","sql":"SELECT * FROM `business_bank_cards` WHERE `user_id` = ? and `card_status` = ? ORDER BY id, [7 1]","duration":"24.4266ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-08-27 10:56:04.437]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f80397267d3447fe01046","sql":"SELECT * FROM `risk_evaluations` WHERE `customer_id` = ? ORDER BY evaluation_time DESC, [7]","duration":"27.2342ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-08-27 10:56:04.438]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f80397267d3447fe01046","sql":"SELECT count(*) as count FROM collection_record cr LEFT JOIN business_account ba ON cr.admin_id = ba.id WHERE (cr.bill_id in (select id from business_repayment_bills where order_id = 89)) LIMIT 1, []","duration":"29.9673ms","duration_ms":29}
{"level":"dev.info","ts":"[2025-08-27 10:56:04.500]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f80397267d3447fe01046","sql":"SELECT \n\tba.username as recorder,\n\tcr.result as result,\n\tcr.note as note,\n\tcr.bill_id as bill_id,\n\tDATE_FORMAT(cr.created_at, '%Y-%m-%d %H:%i:%s') as record_time\n\t FROM collection_record cr LEFT JOIN business_account ba ON cr.admin_id = ba.id WHERE (cr.bill_id in (select id from business_repayment_bills where order_id = 89)) ORDER BY cr.id DESC LIMIT 20, []","duration":"61.7496ms","duration_ms":61}
{"level":"dev.info","ts":"[2025-08-27 10:56:04.503]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f80397267d3447fe01046","sql":"SELECT * FROM `risk_raw_data` WHERE `evaluation_id` = ? LIMIT 1, [EVAL_7_1756110858976228500]","duration":"64.4073ms","duration_ms":64}
{"level":"dev.info","ts":"[2025-08-27 10:56:04.620]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f80397267d3447fe01046","sql":"SELECT * FROM `risk_raw_data` WHERE `evaluation_id` = ? LIMIT 1, [EVAL_7_1756110858976228500]","duration":"116.0712ms","duration_ms":116}
{"level":"dev.info","ts":"[2025-08-27 10:56:04.684]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f80399d95aff006c47aae","sql":"SELECT * FROM `business_loan_orders` WHERE `order_no` = ? LIMIT 1, [******************]","duration":"41.5258ms","duration_ms":41}
{"level":"dev.info","ts":"[2025-08-27 10:56:04.750]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f80399d95aff006c47aae","sql":"\n\t\tSELECT name\n\t\tFROM business_app_account\n\t\tWHERE id = ?\n\t, [7]","duration":"65.6269ms","duration_ms":65}
{"level":"dev.info","ts":"[2025-08-27 10:56:04.811]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f80399d95aff006c47aae","sql":"\n\t\tSELECT updated_at\n\t\tFROM risk_evaluations\n\t\tWHERE customer_id = ? AND risk_result = 0\n\t\tORDER BY updated_at DESC\n\t\tLIMIT 1\n\t, [7]","duration":"61.6518ms","duration_ms":61}
{"level":"dev.info","ts":"[2025-08-27 10:56:04.835]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f80399d95aff006c47aae","sql":"\n\t\tSELECT name\n\t\tFROM business_account\n\t\tWHERE id = ?\n\t, [1]","duration":"23.8167ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-27 10:56:07.035]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f803a292940681fdd4777","sql":"SELECT * FROM `business_loan_orders` WHERE `order_no` = ? LIMIT 1, [******************]","duration":"49.5445ms","duration_ms":49}
{"level":"dev.info","ts":"[2025-08-27 10:56:07.042]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f803a292940681fdd4777","sql":"SELECT * FROM `business_payment_transactions` WHERE (deleted_at IS NULL) and `order_no` = ? ORDER BY created_at DESC, [******************]","duration":"56.4251ms","duration_ms":56}
{"level":"dev.info","ts":"[2025-08-27 10:56:08.199]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f803a6c99852442ecbd9b","sql":"SELECT * FROM `business_loan_orders` WHERE `order_no` = ? LIMIT 1, [******************]","duration":"83.0311ms","duration_ms":83}
{"level":"dev.info","ts":"[2025-08-27 10:56:08.200]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f803a6c99852442ecbd9b","sql":"SELECT * FROM `business_payment_transactions` WHERE (deleted_at IS NULL) and `order_no` = ? ORDER BY created_at DESC, [******************]","duration":"83.5446ms","duration_ms":83}
{"level":"dev.info","ts":"[2025-08-27 10:56:08.364]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f803a773879e0d8f27a4a","sql":"SELECT * FROM `business_loan_orders` WHERE `order_no` = ? LIMIT 1, [******************]","duration":"69.064ms","duration_ms":69}
{"level":"dev.info","ts":"[2025-08-27 10:56:08.364]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f803a773879e0d8f27a4a","sql":"SELECT * FROM `business_payment_transactions` WHERE (deleted_at IS NULL) and `order_no` = ? ORDER BY created_at DESC, [******************]","duration":"69.6362ms","duration_ms":69}
{"level":"dev.info","ts":"[2025-08-27 10:56:09.143]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f803a80a3e9381aade6ea","sql":"SELECT * FROM `business_payment_transactions` WHERE (deleted_at IS NULL) and `order_no` = ? ORDER BY created_at DESC, [******************]","duration":"690.3491ms","duration_ms":690}
{"level":"dev.info","ts":"[2025-08-27 10:56:09.143]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f803a80a3e9381aade6ea","sql":"SELECT * FROM `business_loan_orders` WHERE `order_no` = ? LIMIT 1, [******************]","duration":"690.8564ms","duration_ms":690}
{"level":"dev.info","ts":"[2025-08-27 10:56:09.220]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f803a89a290e802486de8","sql":"SELECT * FROM `business_loan_orders` WHERE `order_no` = ? LIMIT 1, [******************]","duration":"76.1687ms","duration_ms":76}
{"level":"dev.info","ts":"[2025-08-27 10:56:09.222]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f803a89a290e802486de8","sql":"SELECT * FROM `business_payment_transactions` WHERE (deleted_at IS NULL) and `order_no` = ? ORDER BY created_at DESC, [******************]","duration":"78.1921ms","duration_ms":78}
{"level":"dev.info","ts":"[2025-08-27 10:56:09.298]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f803a9371ba54d5f84785","sql":"SELECT * FROM `business_payment_transactions` WHERE (deleted_at IS NULL) and `order_no` = ? ORDER BY created_at DESC, [******************]","duration":"75.0399ms","duration_ms":75}
{"level":"dev.info","ts":"[2025-08-27 10:56:09.299]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f803a9371ba54d5f84785","sql":"SELECT * FROM `business_loan_orders` WHERE `order_no` = ? LIMIT 1, [******************]","duration":"75.6016ms","duration_ms":75}
{"level":"dev.info","ts":"[2025-08-27 10:56:09.327]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f803a9bddaef0f0e67c9f","sql":"SELECT * FROM `business_loan_orders` WHERE `order_no` = ? LIMIT 1, [******************]","duration":"26.1628ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-08-27 10:56:09.353]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f803a9bddaef0f0e67c9f","sql":"SELECT * FROM `business_payment_transactions` WHERE (deleted_at IS NULL) and `order_no` = ? ORDER BY created_at DESC, [******************]","duration":"52.0568ms","duration_ms":52}
{"level":"dev.info","ts":"[2025-08-27 10:57:05.627]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f80475635b408f998ada6","sql":"SELECT * FROM `business_loan_orders` WHERE `order_no` = ? LIMIT 1, [******************]","duration":"50.2447ms","duration_ms":50}
{"level":"dev.info","ts":"[2025-08-27 10:57:05.693]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f80475635b408f998ada6","sql":"SELECT * FROM `business_payment_transactions` WHERE (deleted_at IS NULL) and `order_no` = ? ORDER BY created_at DESC, [******************]","duration":"116.1013ms","duration_ms":116}
{"level":"dev.info","ts":"[2025-08-27 10:57:08.619]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f80480786cc108aec7101","sql":"SELECT * FROM `business_loan_orders` WHERE `order_no` = ? LIMIT 1, [******************]","duration":"67.8176ms","duration_ms":67}
{"level":"dev.info","ts":"[2025-08-27 10:57:08.624]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f80480786cc108aec7101","sql":"SELECT * FROM `business_payment_transactions` WHERE (deleted_at IS NULL) and `order_no` = ? ORDER BY created_at DESC, [******************]","duration":"72.1334ms","duration_ms":72}
{"level":"dev.info","ts":"[2025-08-27 10:57:14.360]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f80495e16d40cc2080953","sql":"SELECT * FROM `business_loan_orders` WHERE `order_no` = ? LIMIT 1, [******************]","duration":"62.5108ms","duration_ms":62}
{"level":"dev.info","ts":"[2025-08-27 10:57:14.392]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f80495e16d40cc2080953","sql":"SELECT * FROM `business_payment_transactions` WHERE (deleted_at IS NULL) and `order_no` = ? ORDER BY created_at DESC, [******************]","duration":"94.2499ms","duration_ms":94}
{"level":"dev.info","ts":"[2025-08-27 10:57:16.420]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f804967bf7180076f1b3c","sql":"SELECT * FROM `business_loan_orders` WHERE `order_no` = ? LIMIT 1, [******************]","duration":"26.7834ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-08-27 10:57:16.420]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185f804967bf7180076f1b3c","sql":"SELECT * FROM `business_payment_transactions` WHERE (deleted_at IS NULL) and `order_no` = ? ORDER BY created_at DESC, [******************]","duration":"26.7834ms","duration_ms":26}
