

[31m2025/08/27 10:56:04 [Recovery] 2025/08/27 - 10:56:04 panic recovered:
GET /business/http://************:8108/https://fincore.oss-cn-heyuan.aliyuncs.com/uploads/bcb09dee6696b9884216feea25140c10_20250818170742.jpg HTTP/1.1
Host: ************:8108
Accept: image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9,en;q=0.8
Connection: close
Referer: http://localhost:9106/
Sec-Ch-Ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
Sec-Ch-Ua-Mobile: ?0
Sec-Ch-Ua-Platform: "Windows"
Sec-Fetch-Dest: image
Sec-Fetch-Mode: no-cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36


token 不存在
D:/work/code/fincore/go/src/route/middleware/JwtVerify.go:115 (0x12da5a4)
	JwtVerify: panic("token 不存在")
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe2ab79)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/validityAPi.go:19 (0x12dbd3d)
	ValidityAPi.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe2ab79)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/LimitHandler.go:31 (0x12db164)
	LimitHandler.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe2ab79)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:176 (0xf9f029)
	ErrorLogMiddleware.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe2ab79)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0xe3e0bc)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe2ab79)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0xe3c985)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe2ab79)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/operate_log.go:101 (0x12dc664)
	OperateLogHandler.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe2ab79)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:142 (0xf9f445)
	AccessLogMiddleware.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe2ab79)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:40 (0xfa05b5)
	RequestIDMiddleware.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe2ab79)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0xe3e0bc)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe2ab79)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0xe3c985)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe2ab79)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:652 (0xe3b0bc)
	serveError: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:645 (0xe3adcd)
	(*Engine).handleHTTPRequest: serveError(c, http.StatusNotFound, default404Body)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572 (0xe3a53b)
	(*Engine).ServeHTTP: engine.handleHTTPRequest(c)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301 (0xb52eb6)
	serverHandler.ServeHTTP: handler.ServeHTTP(rw, req)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102 (0xb1fd74)
	(*conn).serve: serverHandler{c.server}.ServeHTTP(w, w.req)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/asm_amd64.s:1700 (0x6a20e0)
	goexit: BYTE	$0x90	// NOP
[0m


[31m2025/08/27 10:56:04 [Recovery] 2025/08/27 - 10:56:04 panic recovered:
GET /business/http://************:8108/https://fincore.oss-cn-heyuan.aliyuncs.com/uploads/885cf6a8fd6d0bfd20953e0f77ee10d8_20250818170747.jpg HTTP/1.1
Host: ************:8108
Accept: image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9,en;q=0.8
Connection: close
Referer: http://localhost:9106/
Sec-Ch-Ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
Sec-Ch-Ua-Mobile: ?0
Sec-Ch-Ua-Platform: "Windows"
Sec-Fetch-Dest: image
Sec-Fetch-Mode: no-cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36


token 不存在
D:/work/code/fincore/go/src/route/middleware/JwtVerify.go:115 (0x12da5a4)
	JwtVerify: panic("token 不存在")
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe2ab79)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/validityAPi.go:19 (0x12dbd3d)
	ValidityAPi.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe2ab79)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/LimitHandler.go:31 (0x12db164)
	LimitHandler.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe2ab79)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:176 (0xf9f029)
	ErrorLogMiddleware.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe2ab79)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0xe3e0bc)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe2ab79)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0xe3c985)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe2ab79)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/operate_log.go:101 (0x12dc664)
	OperateLogHandler.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe2ab79)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:142 (0xf9f445)
	AccessLogMiddleware.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe2ab79)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:40 (0xfa05b5)
	RequestIDMiddleware.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe2ab79)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0xe3e0bc)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe2ab79)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0xe3c985)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe2ab79)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:652 (0xe3b0bc)
	serveError: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:645 (0xe3adcd)
	(*Engine).handleHTTPRequest: serveError(c, http.StatusNotFound, default404Body)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572 (0xe3a53b)
	(*Engine).ServeHTTP: engine.handleHTTPRequest(c)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301 (0xb52eb6)
	serverHandler.ServeHTTP: handler.ServeHTTP(rw, req)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102 (0xb1fd74)
	(*conn).serve: serverHandler{c.server}.ServeHTTP(w, w.req)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/asm_amd64.s:1700 (0x6a20e0)
	goexit: BYTE	$0x90	// NOP
[0m
